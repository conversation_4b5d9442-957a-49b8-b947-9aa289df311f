PODS:
  - boost-for-react-native (1.63.0)
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.64.2)
  - FBReactNativeSpec (0.64.2):
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.2)
    - RCTTypeSafety (= 0.64.2)
    - React-Core (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - Firebase/AdMob (7.11.0):
    - Firebase/CoreOnly
    - Google-Mobile-Ads-SDK (~> 7.66)
  - Firebase/Analytics (7.11.0):
    - Firebase/Core
  - Firebase/Core (7.11.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 7.11.0)
  - Firebase/CoreOnly (7.11.0):
    - FirebaseCore (= 7.11.0)
  - Firebase/Messaging (7.11.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 7.11.0)
  - FirebaseAnalytics (7.11.0):
    - FirebaseAnalytics/AdIdSupport (= 7.11.0)
    - FirebaseCore (~> 7.0)
    - FirebaseInstallations (~> 7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.0)
    - GoogleUtilities/MethodSwizzler (~> 7.0)
    - GoogleUtilities/Network (~> 7.0)
    - "GoogleUtilities/NSData+zlib (~> 7.0)"
    - nanopb (~> 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (7.11.0):
    - FirebaseAnalytics/Base (= 7.11.0)
    - FirebaseCore (~> 7.0)
    - FirebaseInstallations (~> 7.0)
    - GoogleAppMeasurement/AdIdSupport (= 7.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.0)
    - GoogleUtilities/MethodSwizzler (~> 7.0)
    - GoogleUtilities/Network (~> 7.0)
    - "GoogleUtilities/NSData+zlib (~> 7.0)"
    - nanopb (~> 2.30908.0)
  - FirebaseAnalytics/Base (7.11.0):
    - FirebaseCore (~> 7.0)
    - FirebaseInstallations (~> 7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.0)
    - GoogleUtilities/MethodSwizzler (~> 7.0)
    - GoogleUtilities/Network (~> 7.0)
    - "GoogleUtilities/NSData+zlib (~> 7.0)"
    - nanopb (~> 2.30908.0)
  - FirebaseCore (7.11.0):
    - FirebaseCoreDiagnostics (~> 7.4)
    - GoogleUtilities/Environment (~> 7.0)
    - GoogleUtilities/Logger (~> 7.0)
  - FirebaseCoreDiagnostics (7.11.0):
    - GoogleDataTransport (~> 8.4)
    - GoogleUtilities/Environment (~> 7.0)
    - GoogleUtilities/Logger (~> 7.0)
    - nanopb (~> 2.30908.0)
  - FirebaseInstallations (7.11.0):
    - FirebaseCore (~> 7.0)
    - GoogleUtilities/Environment (~> 7.0)
    - GoogleUtilities/UserDefaults (~> 7.0)
    - PromisesObjC (~> 1.2)
  - FirebaseInstanceID (7.11.0):
    - FirebaseCore (~> 7.0)
    - FirebaseInstallations (~> 7.0)
    - GoogleUtilities/Environment (~> 7.0)
    - GoogleUtilities/UserDefaults (~> 7.0)
  - FirebaseMessaging (7.11.0):
    - FirebaseCore (~> 7.0)
    - FirebaseInstallations (~> 7.0)
    - FirebaseInstanceID (~> 7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.0)
    - GoogleUtilities/Environment (~> 7.0)
    - GoogleUtilities/Reachability (~> 7.0)
    - GoogleUtilities/UserDefaults (~> 7.0)
  - Flipper (0.75.1):
    - Flipper-Folly (~> 2.5)
    - Flipper-RSocket (~> 1.3)
  - Flipper-DoubleConversion (1.1.7)
  - Flipper-Folly (2.5.3):
    - boost-for-react-native
    - Flipper-DoubleConversion
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.180)
  - Flipper-Glog (0.3.6)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.3.1):
    - Flipper-Folly (~> 2.5)
  - FlipperKit (0.75.1):
    - FlipperKit/Core (= 0.75.1)
  - FlipperKit/Core (0.75.1):
    - Flipper (~> 0.75.1)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
  - FlipperKit/CppBridge (0.75.1):
    - Flipper (~> 0.75.1)
  - FlipperKit/FBCxxFollyDynamicConvert (0.75.1):
    - Flipper-Folly (~> 2.5)
  - FlipperKit/FBDefines (0.75.1)
  - FlipperKit/FKPortForwarding (0.75.1):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.75.1)
  - FlipperKit/FlipperKitLayoutPlugin (0.75.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.75.1)
  - FlipperKit/FlipperKitNetworkPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.75.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - glog (0.3.5)
  - Google-Mobile-Ads-SDK (7.69.0):
    - GoogleAppMeasurement (~> 7.0)
    - GoogleUserMessagingPlatform (~> 1.1)
  - GoogleAppMeasurement (7.11.0):
    - GoogleAppMeasurement/AdIdSupport (= 7.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.0)
    - GoogleUtilities/MethodSwizzler (~> 7.0)
    - GoogleUtilities/Network (~> 7.0)
    - "GoogleUtilities/NSData+zlib (~> 7.0)"
    - nanopb (~> 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (7.11.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.0)
    - GoogleUtilities/MethodSwizzler (~> 7.0)
    - GoogleUtilities/Network (~> 7.0)
    - "GoogleUtilities/NSData+zlib (~> 7.0)"
    - nanopb (~> 2.30908.0)
  - GoogleDataTransport (8.4.0):
    - GoogleUtilities/Environment (~> 7.2)
    - nanopb (~> 2.30908.0)
    - PromisesObjC (~> 1.2)
  - GoogleUserMessagingPlatform (1.4.0)
  - GoogleUtilities/AppDelegateSwizzler (7.5.1):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.5.1):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.5.1):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.5.1):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.5.1):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.5.1)"
  - GoogleUtilities/Reachability (7.5.1):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.5.1):
    - GoogleUtilities/Logger
  - hermes-engine (0.7.2)
  - libevent (2.1.12)
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - OpenSSL-Universal (1.1.180)
  - PersonalizedAdConsent (1.0.5)
  - PromisesObjC (1.2.12)
  - RCT-Folly (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
    - RCT-Folly/Default (= 2020.01.13.00)
  - RCT-Folly/Default (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
  - RCT-Folly/Futures (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
    - libevent
  - RCTRequired (0.64.2)
  - RCTTypeSafety (0.64.2):
    - FBLazyVector (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.2)
    - React-Core (= 0.64.2)
  - React (0.64.2):
    - React-Core (= 0.64.2)
    - React-Core/DevSupport (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-RCTActionSheet (= 0.64.2)
    - React-RCTAnimation (= 0.64.2)
    - React-RCTBlob (= 0.64.2)
    - React-RCTImage (= 0.64.2)
    - React-RCTLinking (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - React-RCTSettings (= 0.64.2)
    - React-RCTText (= 0.64.2)
    - React-RCTVibration (= 0.64.2)
  - React-callinvoker (0.64.2)
  - React-Core (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/CoreModulesHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/Default (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/DevSupport (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-jsinspector (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/Hermes (0.64.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2020.01.13.00)
    - RCT-Folly/Futures
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTBlobHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTImageHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTTextHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTWebSocket (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-CoreModules (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/CoreModulesHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTImage (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-cxxreact (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsinspector (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - React-runtimeexecutor (= 0.64.2)
  - React-jsi (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-jsi/Default (= 0.64.2)
  - React-jsi/Default (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
  - React-jsiexecutor (0.64.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-perflogger (= 0.64.2)
  - React-jsinspector (0.64.2)
  - react-native-config (1.4.3):
    - react-native-config/App (= 1.4.3)
  - react-native-config/App (1.4.3):
    - React-Core
  - React-perflogger (0.64.2)
  - React-RCTActionSheet (0.64.2):
    - React-Core/RCTActionSheetHeaders (= 0.64.2)
  - React-RCTAnimation (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTAnimationHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTBlob (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTBlobHeaders (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTImage (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTImageHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTLinking (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - React-Core/RCTLinkingHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTNetwork (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTNetworkHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTSettings (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTSettingsHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTText (0.64.2):
    - React-Core/RCTTextHeaders (= 0.64.2)
  - React-RCTVibration (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTVibrationHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-runtimeexecutor (0.64.2):
    - React-jsi (= 0.64.2)
  - ReactCommon/turbomodule/core (0.64.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.2)
    - React-Core (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-perflogger (= 0.64.2)
  - RNCAsyncStorage (1.15.5):
    - React-Core
  - RNCPushNotificationIOS (1.8.0):
    - React-Core
  - RNFBAdMob (11.5.0):
    - Firebase/AdMob (= 7.11.0)
    - PersonalizedAdConsent (~> 1.0.5)
    - React-Core
    - RNFBApp
  - RNFBAnalytics (11.5.0):
    - Firebase/Analytics (= 7.11.0)
    - React-Core
    - RNFBApp
  - RNFBApp (11.5.0):
    - Firebase/CoreOnly (= 7.11.0)
    - React-Core
  - RNFBMessaging (11.5.0):
    - Firebase/Messaging (= 7.11.0)
    - React-Core
    - RNFBApp
  - RNI18n (2.0.15):
    - React
  - RNIap (6.3.0):
    - React-Core
  - RNRate (1.2.6):
    - React-Core
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase/Analytics (= 7.11.0)
  - Flipper (~> 0.75.1)
  - Flipper-DoubleConversion (= 1.1.7)
  - Flipper-Folly (~> 2.5.3)
  - Flipper-Glog (= 0.3.6)
  - Flipper-PeerTalk (~> 0.0.4)
  - Flipper-RSocket (~> 1.3)
  - FlipperKit (~> 0.75.1)
  - FlipperKit/Core (~> 0.75.1)
  - FlipperKit/CppBridge (~> 0.75.1)
  - FlipperKit/FBCxxFollyDynamicConvert (~> 0.75.1)
  - FlipperKit/FBDefines (~> 0.75.1)
  - FlipperKit/FKPortForwarding (~> 0.75.1)
  - FlipperKit/FlipperKitHighlightOverlay (~> 0.75.1)
  - FlipperKit/FlipperKitLayoutPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitLayoutTextSearchable (~> 0.75.1)
  - FlipperKit/FlipperKitNetworkPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitReactPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitUserDefaultsPlugin (~> 0.75.1)
  - FlipperKit/SKIOSNetworkPlugin (~> 0.75.1)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Google-Mobile-Ads-SDK (~> 7.69)
  - hermes-engine (~> 0.7.2)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/Hermes (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - react-native-config (from `../node_modules/react-native-config`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - "RNFBAdMob (from `../node_modules/@react-native-firebase/admob`)"
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNI18n (from `../node_modules/react-native-i18n`)
  - RNIap (from `../node_modules/react-native-iap`)
  - RNRate (from `../node_modules/react-native-rate`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - boost-for-react-native
    - CocoaAsyncSocket
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseInstallations
    - FirebaseInstanceID
    - FirebaseMessaging
    - Flipper
    - Flipper-DoubleConversion
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - hermes-engine
    - libevent
    - nanopb
    - OpenSSL-Universal
    - PersonalizedAdConsent
    - PromisesObjC
    - YogaKit

EXTERNAL SOURCES:
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  react-native-config:
    :path: "../node_modules/react-native-config"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNFBAdMob:
    :path: "../node_modules/@react-native-firebase/admob"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNI18n:
    :path: "../node_modules/react-native-i18n"
  RNIap:
    :path: "../node_modules/react-native-iap"
  RNRate:
    :path: "../node_modules/react-native-rate"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost-for-react-native: 39c7adb57c4e60d6c5479dd8623128eb5b3f0f2c
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: cf9b38bf0b2d048436d9a82ad2abe1404f11e7de
  FBLazyVector: e686045572151edef46010a6f819ade377dfeb4b
  FBReactNativeSpec: f7b866af6526f63a3b650f2467d4b5230fd98bb4
  Firebase: c121feb35e4126c0b355e3313fa9b487d47319fd
  FirebaseAnalytics: cd3bd84d722a24a8923918af8af8e5236f615d77
  FirebaseCore: 907447d8917a4d3eb0cce2829c5a0ad21d90b432
  FirebaseCoreDiagnostics: 68ad972f99206cef818230f3f3179d52ccfb7f8c
  FirebaseInstallations: a58d4f72ec5861840b84df489f2668d970df558a
  FirebaseInstanceID: ad5135045a498d7775903efd39762d2cdfa1be27
  FirebaseMessaging: 163435fb6db065e3b6228f1e577b10ed2cc506d2
  Flipper: d3da1aa199aad94455ae725e9f3aa43f3ec17021
  Flipper-DoubleConversion: 38631e41ef4f9b12861c67d17cb5518d06badc41
  Flipper-Folly: 755929a4f851b2fb2c347d533a23f191b008554c
  Flipper-Glog: 1dfd6abf1e922806c52ceb8701a3599a79a200a6
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: 127954abe8b162fcaf68d2134d34dc2bd7076154
  FlipperKit: 8a20b5c5fcf9436cac58551dc049867247f64b00
  glog: 73c2498ac6884b13ede40eda8228cb1eee9d9d62
  Google-Mobile-Ads-SDK: 2f288748a42920d1c744946a460896a95b0e9110
  GoogleAppMeasurement: fd19169c3034975cb934e865e5667bfdce59df7f
  GoogleDataTransport: cd9db2180fcecd8da1b561aea31e3e56cf834aa7
  GoogleUserMessagingPlatform: b168e8c46cd8f92aa3e34b584c4ca78a411ce367
  GoogleUtilities: 3df19e3c24f7bbc291d8b5809aa6b0d41e642437
  hermes-engine: 7d97ba46a1e29bacf3e3c61ecb2804a5ddd02d4f
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  OpenSSL-Universal: 1aa4f6a6ee7256b83db99ec1ccdaa80d10f9af9b
  PersonalizedAdConsent: dbecabb3467df967c16d9cebc2ef4a8890e4bbd8
  PromisesObjC: 3113f7f76903778cf4a0586bd1ab89329a0b7b97
  RCT-Folly: ec7a233ccc97cc556cf7237f0db1ff65b986f27c
  RCTRequired: 6d3e854f0e7260a648badd0d44fc364bc9da9728
  RCTTypeSafety: c1f31d19349c6b53085766359caac425926fafaa
  React: bda6b6d7ae912de97d7a61aa5c160db24aa2ad69
  React-callinvoker: 9840ea7e8e88ed73d438edb725574820b29b5baa
  React-Core: b5e385da7ce5f16a220fc60fd0749eae2c6120f0
  React-CoreModules: 17071a4e2c5239b01585f4aa8070141168ab298f
  React-cxxreact: 9be7b6340ed9f7c53e53deca7779f07cd66525ba
  React-jsi: 67747b9722f6dab2ffe15b011bcf6b3f2c3f1427
  React-jsiexecutor: 80c46bd381fd06e418e0d4f53672dc1d1945c4c3
  React-jsinspector: cc614ec18a9ca96fd275100c16d74d62ee11f0ae
  react-native-config: 387b1ea507bc50a2059e69149dac1342b9532f58
  React-perflogger: 25373e382fed75ce768a443822f07098a15ab737
  React-RCTActionSheet: af7796ba49ffe4ca92e7277a5d992d37203f7da5
  React-RCTAnimation: 6a2e76ab50c6f25b428d81b76a5a45351c4d77aa
  React-RCTBlob: 02a2887023e0eed99391b6445b2e23a2a6f9226d
  React-RCTImage: ce5bf8e7438f2286d9b646a05d6ab11f38b0323d
  React-RCTLinking: ccd20742de14e020cb5f99d5c7e0bf0383aefbd9
  React-RCTNetwork: dfb9d089ab0753e5e5f55fc4b1210858f7245647
  React-RCTSettings: b14aef2d83699e48b410fb7c3ba5b66cd3291ae2
  React-RCTText: 41a2e952dd9adc5caf6fb68ed46b275194d5da5f
  React-RCTVibration: 24600e3b1aaa77126989bc58b6747509a1ba14f3
  React-runtimeexecutor: a9904c6d0218fb9f8b19d6dd88607225927668f9
  ReactCommon: 149906e01aa51142707a10665185db879898e966
  RNCAsyncStorage: 8324611026e8dc3706f829953aa6e3899f581589
  RNCPushNotificationIOS: 5b1cf9ad2aaa107ecb92d5d2d7005ba521b2b97a
  RNFBAdMob: a2b5f148468b16d02543707d4627bc9a19016bf3
  RNFBAnalytics: 3e250066a98d6ea0a7816c52de75f2633f726580
  RNFBApp: 9c90f9d576861947ba75fe0830567c570bcbcf6d
  RNFBMessaging: c873e6c37f69d22de22cab134413030a9c6330a9
  RNI18n: e2f7e76389fcc6e84f2c8733ea89b92502351fd8
  RNIap: 2b1c54e3d9350a580f2a5025efc9e1e38819af19
  RNRate: e0af7e724e5fcf89578dbd22ab6395c85402ef29
  Yoga: 575c581c63e0d35c9a83f4b46d01d63abc1100ac
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: 176a2318892079efb00af4295f113bb72e5ea6a5

COCOAPODS: 1.10.2
