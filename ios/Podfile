require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'


platform :ios, '10.0'
# add the Firebase pod for Google Analytics
pod 'Firebase/Analytics', '7.11.0'
pod 'Google-Mobile-Ads-SDK', '~> 7.69'
pod 'RNI18n', :path => '../node_modules/react-native-i18n'
# or pod 'Firebase/AnalyticsWithoutAdIdSupport'
# for Analytics without IDFA collection capability

# add pods for any other desired Firebase products
# https://firebase.google.com/docs/ios/setup#available-pods
target 'BankSms' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => true
  )

  target 'BankSmsTests' do
    inherit! :complete
    # Pods for testing
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable the next line.
  use_flipper!()

  post_install do |installer|
    react_native_post_install(installer)
  end
end
