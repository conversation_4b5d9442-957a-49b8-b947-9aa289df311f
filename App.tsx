import {StyleSheet} from 'react-native';

import React, {Component} from 'react';
import InfoAndBackground from './src/components/InfoAndBackground';
import AppContextProvider, {AppContext} from './src/contexts/AppContext';
import {strings} from './src/utils/Localization';
import MessageModal from './src/components/MessageModal';
import Header from './src/components/Header';
import Body from './src/components/body';
const {isBankSms} = require('./src/Globals');
import {
  initConnection,
  finishTransaction,
  purchaseErrorListener,
  purchaseUpdatedListener,
  ErrorCode,
  flushFailedPurchasesCachedAsPendingAndroid,
} from 'react-native-iap';

let modalBodyMessage;
let titleMessage;
let purchaseUpdateSubscription;
let purchaseErrorSubscription;

class App extends Component {
  state = {loading: false, showMessage: false};
  showAlert = (body, title) => {
    modalBodyMessage = body;
    titleMessage = title;
    this.setShowMessage(true);
  };
  setShowMessage = showMessage => {
    if (showMessage === false) {
      titleMessage = false;
    }
    this.setState({showMessage: showMessage});
  };
  async componentDidMount() {
    try {
      await initConnection();
      // await RNIap.consumeAllItemsAndroid();
      await flushFailedPurchasesCachedAsPendingAndroid();
    } catch (err) {
      // console.warn(err.code, err.message);
    }
    purchaseUpdateSubscription = purchaseUpdatedListener(async purchase => {
      const receipt = purchase.transactionReceipt;
      if (receipt) {
        try {
          //is consumable or not
          let isConsumable = !receipt.includes('autoRenewing');
          // console.log(receipt);
          // console.log(isConsumable);
          // if (isConsumable)
          //   await acknowledgePurchaseAndroid(purchase.purchaseToken);
          await finishTransaction({purchase, isConsumable});
          let message = '_successSubscribe';
          if (isConsumable) {
            message = '_receiptBody';
          }
          this.showAlert(strings(message), strings('_receiptTitle'));
        } catch (err) {
          // console.error('111111', err.code, err.message);
          if (err.code === undefined) {
            this.showAlert(strings('_requestProcessing'));
          } else if (err.code === ErrorCode.E_DEVELOPER_ERROR) {
            this.showAlert(err.message);
          } else {
            this.showAlert('finishTransaction, ' + err.message);
          }
        }
      }
    });

    purchaseErrorSubscription = purchaseErrorListener(err => {
      // console.log(err);
      if (err.code === ErrorCode.E_UNKNOWN) {
        this.showAlert(strings('_paymentDecline'));
      } else if (err.code === ErrorCode.E_ALREADY_OWNED) {
        this.showAlert(strings('_stillProcessing'));
      } else if (err.code === ErrorCode.E_USER_CANCELLED) {
        this.showAlert(strings('E_USER_CANCELLED'));
      } else if (
        err.code === ErrorCode.E_SERVICE_ERROR ||
        err.code === 'PROMISE_BUY_ITEM'
      ) {
        this.showAlert(strings('E_SERVICE_ERROR'));
      } else if (err.code === ErrorCode.E_DEVELOPER_ERROR) {
        this.showAlert(err.message);
      } else {
        // console.log('purchaseErrorListener ', err.code, err.message);
        this.showAlert(err.message);
      }
    });
  }

  componentWillUnmount() {
    if (purchaseUpdateSubscription) {
      purchaseUpdateSubscription.remove();
      purchaseUpdateSubscription = null;
    }
    if (purchaseErrorSubscription) {
      purchaseErrorSubscription.remove();
      purchaseErrorSubscription = null;
    }
  }

  render() {
    const {showMessage} = this.state;
    return (
      <AppContextProvider>
        <AppContext.Consumer>
          {({language}) => (
            <InfoAndBackground>
              <Header style={styles.header} />
              <Body
                style={isBankSms ? styles.banksmsContent : styles.content}
              />
              <MessageModal
                modalVisible={showMessage}
                setModalVisible={this.setShowMessage}
                bodyMessage={modalBodyMessage}
                titleMessage={titleMessage}
              />
            </InfoAndBackground>
          )}
        </AppContext.Consumer>
      </AppContextProvider>
    );
  }
}
const styles = StyleSheet.create({
  header: {
    flex: 80,
  },
  banksmsContent: {
    flex: 60,
  },
  content: {
    flex: 30,
  },
  button: {height: '50%'},
});

App.contextType = AppContext;
export default App;
