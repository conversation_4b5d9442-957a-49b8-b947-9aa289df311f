import {StyleSheet} from 'react-native';

import React, {Component} from 'react';
import InfoAndBackground from './src/components/InfoAndBackground';
import AppContextProvider, {AppContext} from './src/contexts/AppContext';
import {strings} from './src/utils/Localization';
import MessageModal from './src/components/MessageModal';
import Header from './src/components/Header';
import Body from './src/components/body';
const {isBankSms} = require('./src/Globals');
import {
  initConnection,
  finishTransaction,
  purchaseErrorListener,
  purchaseUpdatedListener,
  flushFailedPurchasesCachedAsPendingAndroid,
} from 'react-native-iap';
import { PaymentErrorHandler } from './src/utils/PaymentErrors';
import {AdManager} from 'react-native-admob-native-ads';
import {requestTrackingPermission} from 'react-native-tracking-transparency';
import { EmitterSubscription } from 'react-native';

let modalBodyMessage: string | undefined;
let titleMessage: string | false | undefined;

let purchaseUpdateSubscription: EmitterSubscription | null = null;
let purchaseErrorSubscription: EmitterSubscription | null = null;

class App extends Component {
  state = {loading: false, showMessage: false};
  showAlert = (body: string, title?: string) => {
    modalBodyMessage = body;
    titleMessage = title;
    this.setShowMessage(true);
  };
  setShowMessage = (showMessage: boolean) => {
    if (showMessage === false) {
      titleMessage = false;
    }
    this.setState({showMessage: showMessage});
  };
  async componentDidMount() {
    const trackingStatus = await requestTrackingPermission();

    let trackingAuthorized = false;
    if (trackingStatus === 'authorized' || trackingStatus === 'unavailable') {
      trackingAuthorized = true;
    }

    await AdManager.setRequestConfiguration({
      trackingAuthorized,
    });

    try {
      await initConnection();
      // await RNIap.consumeAllItemsAndroid();
      await flushFailedPurchasesCachedAsPendingAndroid();
    } catch (err) {
      // console.warn(err.code, err.message);
    }
    purchaseUpdateSubscription = purchaseUpdatedListener(async purchase => {
      const receipt = purchase.transactionReceipt;
      if (!receipt) return;

      try {
        const isConsumable = !receipt.includes('autoRenewing');
        await finishTransaction({purchase, isConsumable});
        
        const message = isConsumable ? '_receiptBody' : '_successSubscribe';
        this.showAlert(strings(message), strings('_receiptTitle'));
      } catch (err: any) {
        const errorMessage = PaymentErrorHandler.handleFinishTransactionError({
          code: err.code,
          message: err.message
        });
        this.showAlert(errorMessage);
      }
    });

    purchaseErrorSubscription = purchaseErrorListener(err => {
      const errorMessage = PaymentErrorHandler.handlePurchaseError(err);
      this.showAlert(errorMessage);
    });
  }

  componentWillUnmount() {
    if (purchaseUpdateSubscription) {
      purchaseUpdateSubscription.remove();
      purchaseUpdateSubscription = null;
    }
    if (purchaseErrorSubscription) {
      purchaseErrorSubscription.remove();
      purchaseErrorSubscription = null;
    }
  }

  render() {
    const {showMessage} = this.state;
    return (
      <AppContextProvider>
        <AppContext.Consumer>
          {({language}) => (
            <InfoAndBackground>
              <Header style={styles.header} />
              <Body
                style={isBankSms ? styles.banksmsContent : styles.content}
                showAlert={this.showAlert}
              />
              <MessageModal
                modalVisible={showMessage}
                setModalVisible={this.setShowMessage}
                bodyMessage={modalBodyMessage}
                titleMessage={titleMessage}
              />
            </InfoAndBackground>
          )}
        </AppContext.Consumer>
      </AppContextProvider>
    );
  }
}

const styles = StyleSheet.create({
  header: {
    flex: 80,
  },
  banksmsContent: {
    flex: 60,
  },
  content: {
    flex: 30,
  },
  button: {height: '50%'},
});

App.contextType = AppContext;
export default App;
