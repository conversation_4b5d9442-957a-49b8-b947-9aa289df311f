# iOS In-App Purchase Setup Guide

## Current Issues Identified

1. **Invalid Product IDs**: iOS cannot use simple numbers like '007' or '0' as product IDs
2. **Products Not Approved**: Your products show "Waiting for Review" status in App Store Connect
3. **Missing Configuration**: No proper iOS product IDs configured

## Step-by-Step Fix

### 1. Create Proper Product IDs in App Store Connect

For iOS, product IDs must follow this format:

```
com.yourcompany.yourapp.productname
```

Examples:

- `com.yourcompany.banksms.purchase_007`
- `com.yourcompany.banksms.subscription_monthly`

### 2. Update Your Code

#### In `src/components/body.js`:

Replace the placeholder iOS product IDs with your actual approved product IDs from App Store Connect:

```javascript
const itemSkus = Platform.select({
  android: ['007'],
  ios: ['your_actual_ios_product_id_here'], // ← Replace this
});

const itemSubs = Platform.select({
  android: ['2egp'],
  ios: ['your_actual_ios_subscription_id_here'], // ← Replace this
});
```

### 3. App Store Connect Setup Requirements

1. **Create Products**:

   - Go to App Store Connect → Features → In-App Purchases
   - Create consumable products and subscriptions
   - Use proper reverse DNS format for product IDs

2. **Required Information**:

   - Reference Name
   - Product ID (must match your code exactly)
   - Pricing
   - Screenshots (for review)
   - Localized descriptions

3. **Wait for Approval**:
   - Apple must review and approve all IAP products
   - Status must be "Approved" or "Ready to Submit"

### 4. Testing

#### For Development/TestFlight:

1. Use Sandbox environment
2. Create test users in App Store Connect
3. Test with `react-native-iap` in debug mode

#### Common iOS IAP Errors:

- `SKErrorPaymentInvalid` - Invalid product ID
- `SKErrorPaymentCancelled` - User cancelled
- `SKErrorStoreProductNotAvailable` - Product not approved

### 5. Environment Configuration

The `.env.iap` file contains template configuration. Update it with your actual product IDs:

```env
# Android IAP Product IDs
ANDROID_PRODUCT_ID=007
ANDROID_SUBSCRIPTION_ID=2egp

# iOS IAP Product IDs (REPLACE THESE)
IOS_PRODUCT_ID=com.yourcompany.banksms.purchase_007
IOS_SUBSCRIPTION_ID=com.yourcompany.banksms.subscription_monthly
```

### 6. Code Changes Made

The `TransactionButton` component has been updated to:

- Better error handling for iOS
- Proper logging and debugging
- User-friendly error messages
- Platform-specific IAP handling

### 7. Next Steps

1. **Create proper product IDs** in App Store Connect
2. **Wait for Apple's review and approval**
3. **Update the product IDs** in `body.js` with your actual approved IDs
4. **Test thoroughly** in Sandbox environment
5. **Submit for review** once everything works

### 8. Troubleshooting

If you still get "no products found" after fixing product IDs:

- Check that products are approved in App Store Connect
- Verify the product IDs match exactly (case-sensitive)
- Ensure your app version in TestFlight matches the one with IAP configured
- Check that IAP capability is enabled in Xcode project

### 9. Important Notes

- **iOS product IDs are permanent** - cannot be changed once created
- **Testing requires Sandbox environment** - real purchases won't work in development
- **Review process takes time** - plan for 1-2 days for Apple to review IAP products
- **Receipt validation** - implement server-side receipt validation for production

## Support

For react-native-iap specific issues, check:

- [react-native-ap documentation](https://github.com/dooboolab/react-native-iap)
- [Apple IAP documentation](https://developer.apple.com/in-app-purchase/)
