<resources>
<style name="SpinnerStyle" parent="@android:style/Widget.Spinner">
        <item name="android:background">@null</item>
    </style>
    <style name="SpinnerItem" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:gravity">left</item>
        <item name="android:textColor">#000</item>
        <!-- <item name="android:textSize">12dp</item> -->
    </style>
    <style name="SpinnerDropDownItem" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:gravity">center</item>
        <item name="android:textColor">#000</item>
        <!-- <item name="android:textSize">13dp</item> -->
    </style>
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:textColor">#000000</item>
        <item name="android:spinnerStyle">@style/SpinnerStyle</item>
        <item name="android:spinnerItemStyle">@style/SpinnerItem</item>
        <item name="android:spinnerDropDownItemStyle">@style/SpinnerDropDownItem</item>
        <item name="android:forceDarkAllowed">false</item>
    </style>
</resources>