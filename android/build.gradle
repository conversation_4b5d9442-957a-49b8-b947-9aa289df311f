
buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        androidXBrowser = "1.3.0" // Adding this property will avoid defaultAndroidXVersion
        androidXAnnotation = "1.1.0"
        kotlinVersion = "1.8.0"
        ndkVersion = "26.1.10909125"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath 'com.google.gms:google-services:4.3.4'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"

    }
}

apply plugin: "com.facebook.react.rootproject"
