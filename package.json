{"name": "bank-sms", "version": "0.0.1", "private": true, "scripts": {"postinstall": "patch-package", "bankSmsbuild": "react-native run-android --mode=bankSmsDebug --appId com.bank.shortMessage", "balanceCheckbuild": "react-native run-android --mode=balanceCheckDebug --appId com.banksms", "android": "react-native run-android", "ios": "cd ios&&pod install&&cd ..&&react-native run-ios", "android-dev": "adb connect *************:37383 && adb reverse tcp:8081 tcp:8081 && react-native start", "bankSms-release": "cd android && ENVFILE=.env.bankSms ./gradlew bundlebankSmsRelease && cd ../", "balanceCheck-release": "cd android && ENVFILE=.env.balanceCheck  ./gradlew bundlebalanceCheckRelease && cd ../", "android-clean": "cd android && rm -rf app\\src\\main\\res\\drawable-mdpi && ./gradlew clean  && cd ../", "react-bundle": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output ./android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "bankSms-cbr": "npm run android-clean &&  npm run react-bundle && npm run bankSms-release", "balanceCheck-cbr": "npm run android-clean &&  npm run react-bundle && npm run balanceCheck-release", "test-release": "rm -rf tmp  && java -jar bundletool.jar build-apks --bundle=./android/app/build/outputs/bundle/release/app-release.aab --output=./tmp/my_app.apks --ks=./android/app/clone01.keystore --ks-pass=pass:45454545a --ks-key-alias=my-key-alias --key-pass=pass:45454545a && java -jar bundletool.jar install-apks --apks=./tmp/my_app.apks", "test-bankSms": "rm -rf  tmp   && java -jar bundletool.jar build-apks --bundle=./android/app/build/outputs/bundle/bankSmsRelease/app-bankSms-release.aab --output=./tmp/my_app.apks --ks=./android/app/clone01.keystore --ks-pass=pass:45454545a --ks-key-alias=my-key-alias --key-pass=pass:45454545a && java -jar bundletool.jar install-apks --apks=./tmp/my_app.apks", "test-balanceCheck": "rm -rf  tmp   && java -jar bundletool.jar build-apks --bundle=./android/app/build/outputs/bundle/balanceCheckRelease/app-balanceCheck-release.aab --output=./tmp/my_app.apks --ks=./android/app/clone01.keystore --ks-pass=pass:45454545a --ks-key-alias=my-key-alias --key-pass=pass:45454545a && java -jar bundletool.jar install-apks --apks=./tmp/my_app.apks", "brt": "npm run release && npm run test-release", "bankSms-brt": " npm run bankSms-cbr && npm run test-bankSms", "balanceCheck-brt": "npm run balanceCheck-cbr && npm run test-balanceCheck", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4", "dependencies": {"@notifee/react-native": "^5.6.0", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-firebase/analytics": "^15.4.0", "@react-native-firebase/app": "^15.4.0", "@react-native-firebase/messaging": "^15.4.0", "@react-native-picker/picker": "^2.11.2", "react": "18.2.0", "react-native": "0.74.3", "react-native-config": "^1.4.6", "react-native-google-mobile-ads": "^14.1.0", "react-native-i18n": "^2.0.15", "react-native-iap": "^12.15.1", "react-native-rate": "^1.2.9", "react-native-ratings": "^8.1.0", "react-native-tracking-transparency": "^0.1.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.85", "@react-native/eslint-config": "0.74.85", "@react-native/metro-config": "0.74.85", "@react-native/typescript-config": "0.74.85", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "patch-package": "^8.0.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "rmc-picker": "^5.0.10", "typescript": "5.0.4"}, "rnx-kit": {"kitType": "app", "alignDeps": {"requirements": ["react-native@0.69"], "capabilities": ["babel-preset-react-native", "core", "core-android", "core-ios", "jest", "modal", "react", "storage"]}}}