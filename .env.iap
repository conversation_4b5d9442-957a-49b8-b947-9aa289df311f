# IAP Product IDs Configuration
# For iOS, product IDs must follow Apple's naming conventions
# They should be in the format: com.yourcompany.yourapp.productname

# Android IAP Product IDs
ANDROID_PRODUCT_ID=007
ANDROID_SUBSCRIPTION_ID=2egp

# iOS IAP Product IDs (replace with your actual App Store Connect product IDs)
IOS_PRODUCT_ID=com.yourcompany.banksms.purchase_007
IOS_SUBSCRIPTION_ID=com.yourcompany.banksms.subscription_monthly

# Note: For iOS, you MUST:
# 1. Create these products in App Store Connect
# 2. Wait for Apple's review and approval
# 3. Ensure the status is "Ready to Submit" or "Approved"
# 4. Use the exact product identifier from App Store Connect