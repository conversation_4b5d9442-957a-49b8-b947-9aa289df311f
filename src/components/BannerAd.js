/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {Image, Platform, Text, View} from 'react-native';
import config, {Config} from 'react-native-config';
import {
  BannerAd as GoogleBannerAd,
  BannerAdSize,
  NativeAd,
  NativeAdView,
  NativeAsset,
  NativeAssetType,
  NativeMediaAspectRatio,
} from 'react-native-google-mobile-ads';

const BannerAd = React.memo(
  ({
    index,
    backgroundColor,
    adId,
    textColor,
    actionBtnColor,
    loadOnMount = true,
  }) => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(false);
    const [nativeAd, setNativeAd] = useState(null);

    useEffect(() => {
      setLoading(true);
      setError(false);

      try {
        console.log('Creating native ad request for ID:', adId);

        NativeAd.createForAdRequest(adId, {
          aspectRatio: NativeMediaAspectRatio.LANDSCAPE,
        })
          .then(ad => {
            setNativeAd(ad);
            setError(false);
            console.log('Native ad loaded successfully for ID:', adId);
          })
          .catch(nativeError => {
            console.error('Failed to load native ad:', nativeError);
            setError(true);
          })
          .finally(() => {
            setLoading(false);
            console.log('Native ad request completed for ID:', adId);
          });
      } catch (adError) {
        console.error('Error creating native ad request:', adError);
        setError(true);
        setLoading(false);
      }
    }, [adId]);
    if (loading || error || !nativeAd) {
      return (
        <View
          style={{
            width: '100%',
            margin: 0,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <GoogleBannerAd
            size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
            unitId={Config.AdmobBanner}
          />
        </View>
      );
    }

    return (
      <NativeAdView
        nativeAd={nativeAd}
        style={{
          width: '100%',
          alignSelf: 'center',
          backgroundColor: backgroundColor,
          justifyContent: 'center',
        }}>
        <View
          style={{
            width: '100%',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 10,
            borderWidth: 1,
            borderColor: 'black',
            paddingVertical: 10,
          }}>
          {/* <View
            style={{
              width: 15,
              height: 15,
              borderWidth: 1,
              borderRadius: 2,
              borderColor: config.themeColor,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text style={{fontSize: 9, color: config.themeColor}}>Ad</Text>
          </View> */}

          {nativeAd.icon && (
            <NativeAsset assetType={NativeAssetType.ICON}>
              <Image
                source={{uri: nativeAd.icon.url}}
                style={{
                  width: 60,
                  height: 60,
                }}
              />
            </NativeAsset>
          )}

          <View
            style={{
              width: '60%',
              maxWidth: '60%',
              paddingHorizontal: 6,
            }}>
            <NativeAsset assetType={NativeAssetType.HEADLINE}>
              <Text
                style={{
                  fontWeight: 'bold',
                  fontSize: 13,
                  color: textColor,
                }}>
                {nativeAd.headline}
              </Text>
            </NativeAsset>

            {nativeAd.body && (
              <NativeAsset assetType={NativeAssetType.BODY}>
                <Text
                  numberOfLines={2}
                  style={{
                    fontSize: 11,
                    color: textColor,
                  }}>
                  {nativeAd.body}
                </Text>
              </NativeAsset>
            )}

            {nativeAd.advertiser && (
              <NativeAsset assetType={NativeAssetType.ADVERTISER}>
                <Text
                  style={{
                    fontSize: 10,
                    color: textColor,
                  }}>
                  {nativeAd.advertiser}
                </Text>
              </NativeAsset>
            )}

            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              {nativeAd.store !== undefined && (
                <NativeAsset assetType={NativeAssetType.STORE}>
                  <Text
                    style={{
                      fontSize: 12,
                      color: textColor,
                    }}>
                    {nativeAd.store}
                  </Text>
                </NativeAsset>
              )}

              {nativeAd.starRating !== undefined && nativeAd.starRating > 0 && (
                <View style={{width: 65, marginLeft: 10}}>
                  <Text style={{fontSize: 12, color: textColor}}>
                    {nativeAd.starRating} ★
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* Call to Action Button */}
          {nativeAd.callToAction && (
            <NativeAsset assetType={NativeAssetType.CALL_TO_ACTION}>
              <View
                style={{
                  minHeight: 45,
                  paddingHorizontal: 12,
                  justifyContent: 'center',
                  alignItems: 'center',
                  elevation: 10,
                  maxWidth: 100,
                  width: 80,
                  backgroundColor: config.themeColor,
                }}>
                <Text
                  style={{
                    fontSize: 13,
                    flexWrap: 'wrap',
                    textAlign: 'center',
                    color: actionBtnColor,
                    textTransform: 'uppercase',
                  }}>
                  {nativeAd.callToAction}
                </Text>
              </View>
            </NativeAsset>
          )}
        </View>
      </NativeAdView>
    );
  },
);

export default BannerAd;
