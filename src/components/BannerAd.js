/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  DeviceEventEmitter,
  Platform,
  Text,
  View,
} from 'react-native';
import NativeAdView, {
  AdBadge,
  AdvertiserView,
  CallToActionView,
  HeadlineView,
  IconView,
  StarRatingView,
  StoreView,
  TaglineView,
} from 'react-native-admob-native-ads';
import config, {Config} from 'react-native-config';
import {BannerAd, BannerAdSize} from 'react-native-google-mobile-ads';

const AdView = React.memo(
  ({
    index,
    backgroundColor,
    adId,
    textColor,
    actionBtnColor,
    loadOnMount = true,
  }) => {
    const [aspectRatio, setAspectRatio] = useState(1.5);
    const [loading, setLoading] = useState(false);
    const [loaded, setLoaded] = useState(false);
    const [error, setError] = useState(false);
    const nativeAdRef = useRef();

    const onAdFailedToLoad = event => {
      console.log('onAdFailedToLoad', event);
      setError(true);
      setLoading(false);
      /**
       * Sometimes when you try to load an Ad, it will keep failing
       * and you will recieve this error: "The ad request was successful,
       * but no ad was returned due to lack of ad inventory."
       *
       * This error is not a bug or issue with our Library.
       * Just remove the app from your phone & clean your build
       * folders by running ./gradlew clean in /android folder
       * and for iOS clean the project in xcode. Hopefully the error will
       * be gone.
       *
       * [iOS] If you get this error: "Cannot find an ad network adapter with
       * the name(s): com.google.DummyAdapter". The ad inventory is empty in your
       * location. Try using a vpn to get ads in a different location.
       *
       * If you have recently created AdMob IDs for your ads, it might take
       * a few days until the ads will start showing.
       */
      console.log('AD', 'FAILED', event.error?.message);
    };

    const onAdLoaded = () => {
      console.log('AD', 'LOADED', 'Ad has loaded successfully');
    };

    const onAdClicked = () => {
      console.log('AD', 'CLICK', 'User has clicked the Ad');
    };

    const onAdImpression = () => {
      console.log('AD', 'IMPRESSION', 'Ad impression recorded');
    };

    const onNativeAdLoaded = event => {
      // console.log('AD', 'RECIEVED', 'Unified ad  Recieved', event);
      setLoading(false);
      setLoaded(true);
      setError(false);
      setAspectRatio(event.aspectRatio);
    };

    const onAdLeftApplication = () => {
      console.log('AD', 'LEFT', 'Ad left application');
    };

    useEffect(() => {
      /**
       * for previous steps go to List.js file.
       *
       * [STEP III] We will subscribe to onViewableItemsChanged event in all AdViews in the List.
       */
      const onViewableItemsChanged = event => {
        /**
         * [STEP IV] We check if any AdViews are currently viewable.
         */
        let viewableAds = event.viewableItems.filter(
          i => i.key.indexOf('ad') !== -1,
        );

        viewableAds.forEach(adView => {
          if (adView.index === index && !loaded) {
            /**
             * [STEP V] If the ad is viewable and not loaded
             * already, we will load the ad.
             */
            setLoading(true);
            setLoaded(false);
            setError(false);
            console.log('AD', 'IN VIEW', 'Loading ' + index);
            nativeAdRef.current?.loadAd();
          } else {
            /**
             * We will not reload ads or load
             * ads that are not viewable currently
             * to save bandwidth and requests sent
             * to server.
             */
            if (loaded) {
              console.log('AD', 'IN VIEW', 'Loaded ' + index);
            } else {
              console.log('AD', 'NOT IN VIEW', index);
            }
          }
        });
      };
      if (!loadOnMount) {
        DeviceEventEmitter.addListener(
          'onViewableItemsChanged',
          onViewableItemsChanged,
        );
      }

      return () => {
        if (!loadOnMount) {
          DeviceEventEmitter.removeListener(
            'onViewableItemsChanged',
            onViewableItemsChanged,
          );
        }
      };
    }, [index, loadOnMount, loaded]);

    useEffect(() => {
      if (loadOnMount || index <= 15) {
        setLoading(true);
        setLoaded(false);
        setError(false);
        nativeAdRef.current?.loadAd();
      }
      return () => {
        setLoaded(false);
      };
    }, [index, loadOnMount]);
    const getAd = () => {
      if (loading) {
        return (
          <View
            style={{
              width: '100%',
              margin: 0,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text>Loading Ad...</Text>
            <ActivityIndicator size={28} color="#a9a9a9" />
          </View>
        );
      }
      if (error) {
        return (
          <View
            style={{
              width: '100%',
              margin: 0,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <BannerAd
              size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
              unitId={Config.AdmobBanner}
            />
          </View>
        );
      }
      if (loaded) {
        return (
          <View
            style={{
              height: 100,
              width: '100%',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingHorizontal: 10,
              borderWidth: 1,
              borderColor: 'black',
            }}>
            <AdBadge
              style={{
                width: 15,
                height: 15,
                borderWidth: 1,
                borderRadius: 2,
                borderColor: config.themeColor,
              }}
              textStyle={{
                fontSize: 9,
                color: config.themeColor,
              }}
            />
            <IconView
              style={{
                width: 60,
                height: 60,
              }}
            />
            <View
              style={{
                width: '60%',
                maxWidth: '60%',
                paddingHorizontal: 6,
              }}>
              <HeadlineView
                hello="abc"
                style={{
                  fontWeight: 'bold',
                  fontSize: 13,
                  color: textColor,
                }}
              />
              <TaglineView
                numberOfLines={2}
                style={{
                  fontSize: 11,
                  color: textColor,
                }}
              />
              <AdvertiserView
                style={{
                  fontSize: 10,
                  color: textColor,
                }}
              />

              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <StoreView
                  style={{
                    fontSize: 12,
                    color: textColor,
                  }}
                />
                <StarRatingView
                  starSize={12}
                  fullStarColor="orange"
                  emptyStarColor="gray"
                  style={{
                    width: 65,
                    marginLeft: 10,
                  }}
                />
              </View>
            </View>

            <CallToActionView
              style={[
                {
                  minHeight: 45,
                  paddingHorizontal: 12,
                  justifyContent: 'center',
                  alignItems: 'center',
                  elevation: 10,
                  maxWidth: 100,
                  width: 80,
                },
                Platform.OS === 'ios'
                  ? {
                      backgroundColor: '#00ff00',
                      borderRadius: 5,
                    }
                  : {},
              ]}
              buttonAndroidStyle={{
                backgroundColor: config.themeColor,
                borderRadius: 5,
              }}
              allCaps
              textStyle={{
                fontSize: 13,
                flexWrap: 'wrap',
                textAlign: 'center',
                color: actionBtnColor,
              }}
            />
          </View>
        );
      }
    };
    return (
      <NativeAdView
        ref={nativeAdRef}
        onAdLoaded={onAdLoaded}
        onAdFailedToLoad={onAdFailedToLoad}
        onAdLeftApplication={onAdLeftApplication}
        onAdClicked={onAdClicked}
        onAdImpression={onAdImpression}
        onNativeAdLoaded={onNativeAdLoaded}
        refreshInterval={60000 * 2}
        style={{
          height: 100,
          width: '100%',
          alignSelf: 'center',
          backgroundColor: backgroundColor,
          justifyContent: 'center',
        }}
        videoOptions={{
          customControlsRequested: true,
        }}
        adUnitID={adId} // REPLACE WITH NATIVE_AD_VIDEO_ID for video ads.
      >
        <View
          style={{
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          {getAd()}
        </View>
      </NativeAdView>
    );
  },
);

export default AdView;
