import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Platform,
  Linking,
  Modal,
} from 'react-native';
import {strings} from '../utils/Localization';
import {AirbnbRating} from 'react-native-ratings';
import {rateApp} from './RateApp';
import analytics from '@react-native-firebase/analytics';
import BannerAd from './BannerAd';
import Config from 'react-native-config';
const {themeColor, appName} = Config;
class MessageModal extends Component {
  state = {rateus: false, rate: 4};
  render() {
    const {modalVisible, bodyMessage, titleMessage, onOKClicked} = this.props;
    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => this.closeModal()}
        onBackdropPress={() => this.closeModal()}>
        <TouchableOpacity
          style={styles.centeredView}
          activeOpacity={1}
          onPressOut={() => this.closeModal()}>
          <View style={styles.centeredView} onpress>
            <View style={styles.modalView}>
              {titleMessage ? (
                <Text style={styles.title}>{titleMessage}</Text>
              ) : (
                <BannerAd
                  backgroundColor={'transparent'}
                  textColor={'black'}
                  actionBtnColor={'black'}
                  adId={Config.AdMobId_popUp}
                />
              )}

              <Text style={styles.bodyText}>{bodyMessage}</Text>
              {titleMessage && (
                <>
                  <Text style={styles.rateUsTxt}>{strings('_rateUs')}</Text>
                  <AirbnbRating
                    count={5}
                    reviews={[
                      strings('_terrible'),
                      strings('_bad'),
                      strings('_disappointing'),
                      strings('_oK'),
                      strings('_good'),
                    ]}
                    defaultRating={this.state.rate}
                    onFinishRating={this.finishRating}
                  />
                </>
              )}
              <View style={styles.buttons}>
                {this.state.rate === 1 && (
                  <TouchableOpacity
                    style={styles.textButton}
                    onPress={() =>
                      Linking.openURL(
                        `mailto:<EMAIL>?subject=${appName}%20App%20didn't%20work%20as%20expected&body=`,
                      )
                    }>
                    <Text style={styles.okText}>{strings('_contactUs')}</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={styles.textButton}
                  onPress={() => this.closeModal(onOKClicked)}>
                  <Text style={styles.okText}>{strings('_okayButton')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    );
  }
  closeModal = async closeAction => {
    if (this.state.rate !== 4) {
      await analytics().logEvent('rate', {
        rate: this.state.rate,
      });
    }
    this.setState({rateus: false, rate: 4});
    this.props.setModalVisible(false);
    if (closeAction) {
      closeAction();
    }
  };
  finishRating = rate => {
    this.setState({rate: rate});
    if (rate === 5) {
      rateApp();
      this.closeModal();
    }
  };
}

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 20,
    color: themeColor,
    ...Platform.select({
      android: {fontFamily: 'Roboto'},
    }),
  },
  textButton: {
    flex: 1,
    elevation: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttons: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  okText: {
    fontWeight: 'bold',
    textAlign: 'center',
    color: themeColor,
    fontSize: 19,
    ...Platform.select({
      android: {fontFamily: 'Roboto'},
    }),
  },
  rateUsTxt: {
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 19,
    ...Platform.select({
      android: {fontFamily: 'Roboto'},
    }),
  },
  bodyText: {
    fontSize: 15,
    fontWeight: 'bold',
    marginBottom: 15,
    ...Platform.select({
      android: {fontFamily: 'Roboto'},
    }),
  },
});
export default MessageModal;
