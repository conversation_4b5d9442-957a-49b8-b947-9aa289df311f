import React, { Component } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Platform,
  Linking,
  Dimensions,
  PanResponder,
  Animated,
} from 'react-native';
import Modal from 'react-native-modal';
import { strings } from '../utils/Localization';
import { AirbnbRating } from 'react-native-ratings';
import { rateApp } from './RateApp';
import analytics from '@react-native-firebase/analytics';
import BannerAd from './BannerAd';
import Config from 'react-native-config';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const { themeColor, appName } = Config;
const { height } = Dimensions.get('window');

class MessageModal extends Component {
  constructor(props) {
    super(props);
    this.state = { rateus: false, rate: 4 };
    this.panY = new Animated.Value(0);

    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => false,
      onStartShouldSetPanResponderCapture: () => false,
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        const isHeader = evt.nativeEvent.locationY < 60;
        if (isHeader) {
          return Math.abs(gestureState.dy) > Math.abs(gestureState.dx);
        }
        return false;
      },
      onMoveShouldSetPanResponderCapture: () => false,
      onPanResponderGrant: () => {
        this.panY.setOffset(0);
      },
      onPanResponderMove: (e, gesture) => {
        if (gesture.dy > 0) {
          this.panY.setValue(gesture.dy);
        }
      },
      onPanResponderRelease: (e, gesture) => {
        if (gesture.dy > 50) {
          this.closeModal();
        } else {
          Animated.spring(this.panY, {
            toValue: 0,
            useNativeDriver: true,
            bounciness: 0,
          }).start();
        }
      },
    });
  }

  render() {
    const { modalVisible, bodyMessage, titleMessage, onOKClicked } = this.props;
    return (
      <Modal
        isVisible={modalVisible}
        onBackdropPress={this.closeModal}
        style={styles.modal}
        backdropOpacity={0.6}
        animationIn="slideInUp"
        animationOut="slideOutDown"
        animationInTiming={400}
        animationOutTiming={300}
        backdropTransitionInTiming={400}
        backdropTransitionOutTiming={300}
        useNativeDriver={true}
        propagateSwipe={true}>
        <View style={styles.modalContent}>
          <Animated.View
            style={[styles.modalHeader, {
              transform: [{ translateY: this.panY }]
            }]}
            {...this.panResponder.panHandlers}>
            {titleMessage ? (
              <Text style={styles.title}>{titleMessage}</Text>
            ) : (
              <BannerAd
                backgroundColor={'transparent'}
                textColor={'black'}
                actionBtnColor={'black'}
                adId={Config.AdMobId_popUp}
              />
            )}
            <View style={styles.dragIndicator} />
          </Animated.View>

          <View style={styles.contentContainer}>
            <Text style={styles.bodyText}>{bodyMessage}</Text>
            {titleMessage && (
              <View >
                <Text style={styles.rateUsTxt}>{strings('_rateUs')}</Text>
                <AirbnbRating
                  count={5}
                  reviews={[
                    strings('_terrible'),
                    strings('_bad'),
                    strings('_disappointing'),
                    strings('_oK'),
                    strings('_good'),
                  ]}
                  defaultRating={this.state.rate}
                  onFinishRating={this.finishRating}
                  size={30}
                />
              </View>
            )}
          </View>

          <View style={styles.buttons}>
            {this.state.rate === 1 && (
              <TouchableOpacity
                style={[styles.button]}
                onPress={() =>
                  Linking.openURL(
                    `mailto:<EMAIL>?subject=${appName}%20App%20didn't%20work%20as%20expected&body=`,
                  )}>
                <Icon name="email" size={20} color={themeColor} style={styles.buttonIcon} />
                <Text style={styles.buttonText}>{strings('_contactUs')}</Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={() => this.closeModal(onOKClicked)}>
              <Text style={styles.okText}>{strings('_okayButton')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  }

  closeModal = async closeAction => {
    if (this.state.rate !== 4) {
      await analytics().logEvent('rate', {
        rate: this.state.rate,
      });
    }
    this.setState({rateus: false, rate: 4});
    this.props.setModalVisible(false);
    if (closeAction) {
      closeAction();
    }
  };

  finishRating = (rate) => {
    this.setState({ rate: rate });
    if (rate === 5) {
      rateApp();
      this.closeModal();
    }
  };
}

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 8,
    maxHeight: height * 0.8,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalHeader: {
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    zIndex: 1,
    minHeight: 60,
  },
  dragIndicator: {
    width: 40,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#E0E0E0',
    position: 'absolute',
    top: 8,
  },
  contentContainer: {
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: themeColor,
    marginTop: 8,
    ...Platform.select({
      android: { fontFamily: 'Roboto' },
    }),
  },
  bodyText: {
    fontSize: 15,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#424242',
    ...Platform.select({
      android: { fontFamily: 'Roboto' },
    }),
  },
  rateUsTxt: {
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 19,
    marginBottom: 8,
    color: '#212121',
    ...Platform.select({
      android: { fontFamily: 'Roboto' },
    }),
  },
  buttons: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  button: {
    flex: 1,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    marginHorizontal: 6,
  },
  primaryButton: {
    backgroundColor: themeColor,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    fontSize: 15,
    fontWeight: '600',
    color: themeColor,
  },
  primaryButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default MessageModal;
