import React, {Component} from 'react';
import {Platform, StyleSheet, View, Text, ScrollView} from 'react-native';
import TransactionButton from './TransactionButton';
import {strings} from '../utils/Localization';
import BannerAd from './BannerAd';

const itemSkus = Platform.select({
  android: ['007'],
  ios: ['007'],
});
const itemSubs = Platform.select({
  android: [
    '2egp', // subscription
  ],
  ios: ['0'],
});

class Body extends Component {
  render() {
    const {showAlert} = this.props;
    return (
      <View style={this.props.style}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}>
          {/* Transaction Buttons */}
          <View style={styles.buttonsContainer}>
            <TransactionButton
              style={styles.button}
              itemSkus={itemSkus}
              showAlert={showAlert}>
              {strings('_makeTransaction').toUpperCase()}
            </TransactionButton>
            <TransactionButton
              style={styles.button}
              itemSkus={itemSubs}
              showAlert={showAlert}
              subscribe>
              {strings('_subscribeButton').toUpperCase()}
            </TransactionButton>
          </View>
        </ScrollView>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
  },
  scrollView: {
    flexGrow: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 32,
    flexGrow: 1,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  logoText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2196F3',
    letterSpacing: 2,
  },
  logoSubText: {
    fontSize: 24,
    color: '#4CAF50',
    letterSpacing: 4,
    marginTop: -5,
  },
  
  adContainer: {
    marginBottom: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
    minHeight: 100,
  },
  buttonsContainer: {
    marginTop: 8,
  },
  button: {
    marginBottom: 16,
    height: 56,
  },
});

export default Body;
