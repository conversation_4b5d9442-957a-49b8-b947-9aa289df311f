import React, {Component} from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import TransactionButton from './TransactionButton';
import {strings} from '../utils/Localization';
import config from 'react-native-config';

// Use proper iOS product IDs - these should match your App Store Connect configuration
// For now, using placeholder values - replace with your actual approved iOS product IDs
const itemSkus = Platform.select({
  android: ['007'],
  ios: ['007'], // Replace with actual iOS product ID
});

const itemSubs = Platform.select({
  android: ['2egp'],
  ios: ['org.plusTechnology.BankSms.subscription_monthly'], // Replace with actual iOS subscription ID
});
class Body extends Component {
  render() {
    return (
      <View style={this.props.style}>
        <TransactionButton
          style={styles.button}
          itemSkus={itemSkus}
          showAlert={this.showAlert}>
          {strings('_makeTransaction').toUpperCase()}
        </TransactionButton>
        <TransactionButton
          style={styles.button}
          itemSkus={itemSubs}
          showAlert={this.showAlert}
          subscribe>
          {strings('_subscribeButton').toUpperCase()}
        </TransactionButton>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  button: {height: '50%'},
});

export default Body;
