import React, {Component} from 'react';
import {
  Alert,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import {strings} from '../utils/Localization';
import * as RNIap from 'react-native-iap';
import config from 'react-native-config';
import MessageModal from './MessageModal';

const width = Dimensions.get('window').width;

class TransactionButton extends Component {
  state = {loading: false, modalVisible: false};
  render() {
    // console.log(config);
    const {loading} = this.state;
    return <View style={this.props.style}>{this.checkLoading(loading)}</View>;
  }
  getSubscriptions = async () => {
    try {
      console.log('Getting subscriptions for SKUs:', this.props.itemSkus);
      const products = await RNIap.getSubscriptions(this.props.itemSkus);
      console.log('Subscriptions retrieved:', products);

      if (products.length === 0) {
        console.warn('No subscriptions found for the provided SKUs');
        // Don't set state with empty array to avoid breaking the UI
        return;
      }

      this.setState({productList: products});
    } catch (err) {
      console.warn('Error getting subscriptions:', err.code, err.message);
    }
  };
  requestSubscription = async () => {
    try {
      this.setState({loading: true});
      let subList = this.state.subscriptionList;

      if (subList === undefined) {
        console.log('Getting Subscriptions for iOS', this.props.itemSkus);
        try {
          const products = await RNIap.getSubscriptions({
            skus: this.props.itemSkus,
          });

          console.log('Subscriptions found:', products);

          if (products.length === 0) {
            console.warn(
              'No subscriptions found for SKUs:',
              this.props.itemSkus,
            );
            Alert.alert(
              'Subscription Not Available',
              'The subscription product is not available. Please check your App Store Connect configuration.',
            );
            this.setState({loading: false});
            return;
          }

          this.setState({subscriptionList: products});
          subList = products;
        } catch (err) {
          console.error('Error getting subscriptions:', err);
          Alert.alert('Error', 'Failed to fetch subscriptions: ' + err.message);
          this.setState({loading: false});
          return;
        }
      }

      // Check if we have subscriptions
      if (!subList || subList.length === 0) {
        console.warn('No subscriptions available');
        Alert.alert(
          'Subscription Not Available',
          'No subscription products available. Please try again later.',
        );
        this.setState({loading: false});
        return;
      }

      this.setState({modalVisible: true});
    } catch (err) {
      console.error('Subscription request error:', err);
      Alert.alert('Error', 'Failed to process subscription: ' + err.message);
    }
    this.setState({loading: false});
  };
  subscribeCall = async () => {
    try {
      if (
        !this.state.subscriptionList ||
        this.state.subscriptionList.length === 0
      ) {
        console.error('No subscription list available');
        Alert.alert('Error', 'No subscription products available');
        return;
      }

      const subscription = this.state.subscriptionList[0];

      // For iOS, we need to handle subscriptions differently
      const subscriptionParams = {
        sku: this.props.itemSkus[0],
      };

      // Only add subscription offers if they exist (mainly for Android)
      if (
        subscription.subscriptionOfferDetails &&
        subscription.subscriptionOfferDetails.length > 0
      ) {
        subscriptionParams.subscriptionOffers = [
          {
            sku: this.props.itemSkus[0],
            offerToken: subscription.subscriptionOfferDetails[0].offerToken,
          },
        ];
      }

      console.log('Requesting subscription with params:', subscriptionParams);
      await RNIap.requestSubscription(subscriptionParams);
    } catch (err) {
      console.error('Subscription call error:', err);
      Alert.alert('Subscription Failed', err.message);
    }
  };
  // // Version 3 apis
  requestPurchase = async () => {
    try {
      console.log('Requesting Purchase');
      this.setState({loading: true});

      // Check if we need to fetch products
      if (this.state.productList === undefined) {
        console.log('Getting Products for iOS', this.props.itemSkus);
        try {
          const products = await RNIap.getProducts({skus: this.props.itemSkus});
          console.log('Products found:', products);

          if (products.length === 0) {
            console.warn('No products found for SKUs:', this.props.itemSkus);
            Alert.alert(
              'Product Not Available',
              'The in-app purchase product is not available. Please check your App Store Connect configuration.',
            );
            this.setState({loading: false});
            return;
          }

          this.setState({productList: products});
        } catch (err) {
          console.log('Error getting products', err);
          Alert.alert('Error', 'Failed to fetch products: ' + err.message);
          this.setState({loading: false});
          return;
        }
      }

      // Check if we have products to purchase
      if (!this.state.productList || this.state.productList.length === 0) {
        console.warn('No products available for purchase');
        Alert.alert(
          'Product Not Available',
          'No products available for purchase. Please try again later.',
        );
        this.setState({loading: false});
        return;
      }

      console.log('requestPurchase for iOS with SKU:', this.props.itemSkus[0]);
      await RNIap.requestPurchase({
        skus: this.props.itemSkus,
        sku: this.props.itemSkus[0],
      });
    } catch (err) {
      console.error('Purchase error:', err);
      Alert.alert('Purchase Failed', err.message);
    }
    this.setState({loading: false});
  };
  checkLoading = loading => {
    console.log('Loading', this.state.subscriptionList);
    return loading ? (
      <ActivityIndicator
        animating={loading}
        size="large"
        color={config.themeColor}
      />
    ) : (
      <>
        {this.state.subscriptionList && (
          <MessageModal
            modalVisible={this.state.modalVisible}
            bodyMessage={`${strings('_subscribeMessage')} ${
              this.state.subscriptionList[0].subscriptionOfferDetails[0]
                .pricingPhases.pricingPhaseList[0].formattedPrice
            }`}
            onOKClicked={this.subscribeCall}
            setModalVisible={visible => this.setState({modalVisible: visible})}
          />
        )}
        <TouchableOpacity
          style={styles.btn}
          onPress={() => {
            return this.props.subscribe
              ? this.requestSubscription()
              : this.requestPurchase();
          }}
          activeOpacity={1}>
          <Text style={styles.txt}>{this.props.children}</Text>
        </TouchableOpacity>
      </>
    );
  };

  getAvailablePurchases = async () => {
    try {
      await RNIap.getAvailablePurchases().catch(err => {
        this.props.showAlert('getAvailablePurchases, ' + err.message);
      });
    } catch (err) {
      Alert.alert(err.message);
    }
  };
}
const styles = StyleSheet.create({
  btn: {
    width: '60%',
    alignSelf: 'center',
    height: '80%',
    justifyContent: 'center',
    borderRadius: 30,
    backgroundColor: 'ghostwhite',
    shadowColor: config.themeColor,
    elevation: 6,
    shadowRadius: 30,
    borderWidth: 1,
    borderColor: config.themeColor,
  },
  txt: {
    fontSize: parseInt(width / 22.5, 10),
    fontWeight: 'bold',
    color: config.themeColor,
    textAlign: 'center',
  },
});
export default TransactionButton;
