import React, { Component } from 'react';
import {
  Alert,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import config from 'react-native-config';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { strings } from '../utils/Localization';
import * as RNIap from 'react-native-iap';
import MessageModal from './MessageModal';

const width = Dimensions.get('window').width;

class TransactionButton extends Component {
  state = { loading: false, modalVisible: false };

  getSubscriptions = async () => {
    try {
      const products = await RNIap.getSubscriptions(this.props.itemSkus);
      // console.log('Products', products);
      this.setState({ productList: products });
    } catch (err) {
      // console.warn(err.code, err.message);
    }
  };

  requestSubscription = async () => {
    try {
      this.setState({ loading: true });
      let subList = this.state.subscriptionList;
      if (subList === undefined) {
        subList = await RNIap.getSubscriptions({
          skus: this.props.itemSkus,
        })
          .then(products => {
            this.setState({ subscriptionList: products });
            return products;
          })
          .catch(err => {
            // console.error(err);
          });
      }
      this.setState({ modalVisible: true });
    } catch (err) {
      // console.error(err);
    }
    this.setState({ loading: false });
  };
  subscribeCall = async () => {
    console.log('Subscribe Call', this.state.subscriptionList[0].subscriptionOfferDetails[0].offerToken);
    await RNIap.requestSubscription({
      sku: this.props.itemSkus,
      ...(this.state.subscriptionList[0].subscriptionOfferDetails && {
        subscriptionOffers: [
          {
            sku: this.props.itemSkus[0],
            offerToken:
              this.state.subscriptionList[0].subscriptionOfferDetails[0]
                .offerToken,
          },
        ],
      }),
    });
  };
  // // Version 3 apis
  requestPurchase = async () => {
    try {
      console.log('Requesting Purchase');
      this.setState({ loading: true });
      if (this.state.productList === undefined) {
        console.log('Getting Products');
        await RNIap.getProducts({ skus: this.props.itemSkus })
          .then(products => {
            console.log('Products', products);
            this.setState({ productList: products });
          })
          .catch(err => {
            console.log('Error getting products', err);
          });
      }
      await RNIap.requestPurchase({ skus: this.props.itemSkus });
    } catch (err) { }
    this.setState({ loading: false });
  };


  getAvailablePurchases = async () => {
    try {
      await RNIap.getAvailablePurchases().catch(err => {
        this.props.showAlert('getAvailablePurchases, ' + err.message);
      });
    } catch (err) {
      Alert.alert(err.message);
    }
  };
  renderButtonContent = () => {
    const { loading } = this.state;
    const { subscribe } = this.props;

    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#FFFFFF" />
          <Text style={[styles.txt, styles.loadingText]}>Processing...</Text>
        </View>
      );
    }

    return (
      <View style={styles.buttonContent}>
        <Icon
          name={subscribe ? 'calendar-clock' : 'credit-card-check'}
          size={24}
          color="#FFFFFF"
          style={styles.icon}
        />
        <Text style={styles.txt}>{this.props.children}</Text>
      </View>
    );
  };
  render() {
    const { loading } = this.state;
    const { subscribe } = this.props;

    return (
      <View style={this.props.style}>{this.state.subscriptionList && (
        <MessageModal
          modalVisible={this.state.modalVisible}
          bodyMessage={`${strings('_subscribeMessage')} ${this.state.subscriptionList[0].subscriptionOfferDetails[0]
              .pricingPhases.pricingPhaseList[0].formattedPrice
            }`}
          onOKClicked={this.subscribeCall}
          setModalVisible={visible => this.setState({ modalVisible: visible })}
        />
      )}
        <TouchableOpacity
          style={[
            styles.btn,
            subscribe ? styles.subscribeBtn : styles.checkNowBtn,
            loading && styles.btnLoading,
          ]}
          onPress={() => {
            return subscribe ? this.requestSubscription() : this.requestPurchase();
          }}
          disabled={loading}
          activeOpacity={0.8}>
          {this.renderButtonContent()}
        </TouchableOpacity>
      </View>
    );
  }

}
const styles = StyleSheet.create({
  btn: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 28,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.27,
    shadowRadius: 4.65,
    elevation: 6,
  },
  checkNowBtn: {
    backgroundColor: config.themeColor || '#2196F3',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  subscribeBtn: {
    backgroundColor: '#4CAF50',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  btnLoading: {
    opacity: 0.8,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    marginRight: 12,
  },
  txt: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    letterSpacing: 1,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  loadingText: {
    marginLeft: 8,
  },
});
export default TransactionButton;
