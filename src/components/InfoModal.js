import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  // Modal,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
  ScrollView,
  Modal,
} from 'react-native';
import {rateApp} from './RateApp';
import translate from '../resources/translate.png';
import {strings} from '../utils/Localization';
import config from 'react-native-config';

const height = Dimensions.get('window').height;
const width = Dimensions.get('window').width;
class InfoModal extends Component {
  render() {
    const {modalVisible, setModalVisible, setLanguageModalVisible} = this.props;
    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          setModalVisible(false);
        }}
        onBackdropPress={() => setModalVisible(false)}
        propagateSwipe>
        <ScrollView>
          <View style={styles.centeredView}>
            <View style={styles.modalView}>
              <Text style={styles.title}>{strings('_infoTitle')}</Text>
              <Text style={styles.modalText} textBreakStrategy="simple">
                {strings('_infoBody')}
              </Text>
              <View style={styles.buttons}>
                <TouchableOpacity
                  style={{
                    ...styles.openButton,
                  }}
                  onPress={() => {
                    setLanguageModalVisible(true);
                  }}>
                  <Image
                    style={styles.image}
                    source={translate}
                    accessibilityLabel="Translate"
                  />
                </TouchableOpacity>
                <TouchableOpacity style={styles.openButton} onPress={rateApp}>
                  <Text style={styles.textStyle}>{strings('_rateApp')}</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.openButton}
                  onPress={() => {
                    setModalVisible(!modalVisible);
                  }}>
                  <Text style={styles.textStyle}>{strings('_okayButton')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </Modal>
    );
  }
}

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  openButton: {
    flex: 1,
    elevation: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttons: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textStyle: {
    fontWeight: 'bold',
    textAlign: 'center',
    color: config.themeColor,
    fontSize: 19,
    ...Platform.select({
      android: {fontFamily: 'Roboto'},
    }),
  },
  title: {
    fontWeight: 'bold',
    fontSize: 20,
    color: config.themeColor,
    ...Platform.select({
      android: {fontFamily: 'Roboto'},
    }),
  },
  modalText: {
    marginBottom: 15,
    ...Platform.select({
      android: {fontFamily: 'Roboto'},
    }),
  },
  image: {
    tintColor: config.themeColor,
    width: width / 10,
    height: height / 20,
    shadowOffset: {width: 0, height: 0},
    shadowRadius: 5,
    resizeMode: 'contain',
  },
});
export default InfoModal;
