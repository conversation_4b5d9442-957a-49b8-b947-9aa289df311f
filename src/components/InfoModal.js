import React, { Component } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
  PanResponder,
  Animated,
} from 'react-native';
import { rateApp } from './RateApp';
import translate from '../resources/translate.png';
import { strings } from '../utils/Localization';
import Modal from 'react-native-modal';
import config from 'react-native-config';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const { width, height } = Dimensions.get('window');

class InfoModal extends Component {
  constructor(props) {
    super(props);
    this.panY = new Animated.Value(0);

    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => false,
      onStartShouldSetPanResponderCapture: () => false,
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        const isHeader = evt.nativeEvent.locationY < 60;
        if (isHeader) {
          return Math.abs(gestureState.dy) > Math.abs(gestureState.dx);
        }
        return false;
      },
      onMoveShouldSetPanResponderCapture: () => false,
      onPanResponderGrant: () => {
        this.panY.setOffset(0);
      },
      onPanResponderMove: (e, gesture) => {
        if (gesture.dy > 0) {
          this.panY.setValue(gesture.dy);
        }
      },
      onPanResponderRelease: (e, gesture) => {
        if (gesture.dy > 50) {
          this.props.setModalVisible(false);
        } else {
          Animated.spring(this.panY, {
            toValue: 0,
            useNativeDriver: true,
            bounciness: 0,
          }).start();
        }
      },
    });
  }

  render() {
    const { modalVisible, setModalVisible, setLanguageModalVisible } = this.props;

    return (
      <Modal
        isVisible={modalVisible}
        onBackdropPress={() => setModalVisible(false)}
        style={styles.modal}
        backdropOpacity={0.6}
        animationIn="slideInUp"
        animationOut="slideOutDown"
        animationInTiming={400}
        animationOutTiming={300}
        backdropTransitionInTiming={400}
        backdropTransitionOutTiming={300}
        useNativeDriver={true}
        propagateSwipe={true}>
        <View style={styles.modalContent}>
          <Animated.View
            style={[styles.modalHeader, {
              transform: [{ translateY: this.panY }]
            }]}
            {...this.panResponder.panHandlers}>
            <Text style={styles.title}>{strings('_infoTitle')}</Text>
            <View style={styles.dragIndicator} />
          </Animated.View>
          <ScrollView style={styles.contentContainer}>
            {/* Bank Compatibility Warning */}
            <View style={styles.warningContainer}>
              <Text style={styles.warningTitle}>⚠️ {strings('_compatibilityTitle')}</Text>
              <Text style={styles.warningText}>
                {strings('_compatibilityMessage')}
              </Text>
            </View>

            {/* How It Works Section */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>{strings('_howItWorksTitle')}</Text>
              <View style={styles.stepContainer}>
                <View style={styles.stepNumberContainer}>
                  <Text style={styles.stepNumber}>1</Text>
                </View>
                <Text style={styles.stepText}>{strings('_step1')}</Text>
              </View>
              <View style={styles.stepContainer}>
                <View style={styles.stepNumberContainer}>
                  <Text style={styles.stepNumber}>2</Text>
                </View>
                <Text style={styles.stepText}>{strings('_step2')}</Text>
              </View>
              <View style={styles.stepContainer}>
                <View style={styles.stepNumberContainer}>
                  <Text style={styles.stepNumber}>3</Text>
                </View>
                <Text style={styles.stepText}>{strings('_step3')}</Text>
              </View>
            </View>
          </ScrollView>
          <View style={styles.buttons}>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => {
                setLanguageModalVisible(true);
              }}>
              <Image style={styles.translateIcon} source={translate} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={rateApp}>
              <Icon name="star" size={20} color={config.themeColor} style={styles.buttonIcon} />
              <Text style={styles.buttonText}>{strings('_rateApp')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={() => {
                setModalVisible(false);
              }}>
              <Text style={styles.primaryButtonText}>
                {strings('_okayButton')}
              </Text>
            </TouchableOpacity>
          </View>
      </View>
      </Modal >
    );
  }
}

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 8,
    maxHeight: height * 0.8,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    flex: 1,
  },
  modalHeader: {
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    backgroundColor: 'white', // Ensure header is opaque
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    zIndex: 1, // Ensure header stays on top
    minHeight: 60,  // Ensure consistent hit area for pan handler
  },
  dragIndicator: {
    width: 40,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#E0E0E0',
    position: 'absolute',
    top: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212121',
    marginTop: 8,
  },
  contentContainer: {
    padding: 20,
    flex: 1,
  },
  modalText: {
    fontSize: 15,
    color: '#424242',
    lineHeight: 22,
    padding: 20,
  },
  buttons: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    marginRight: 12,
  },
  translateIcon: {
    width: 24,
    height: 24,
    tintColor: config.themeColor,
  },
  button: {
    flex: 1,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    marginHorizontal: 6,
  },
  primaryButton: {
    backgroundColor: config.themeColor || '#2196F3',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    fontSize: 15,
    fontWeight: '600',
    color: config.themeColor,
  },
  primaryButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#FFFFFF',
  }, warningContainer: {
    backgroundColor: '#FFF3E0',
    padding: 16,
    marginBottom: 20,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  warningTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#E65100',
    marginBottom: 8,
  },
  warningText: {
    fontSize: 14,
    color: '#424242',
    lineHeight: 20,
  },
  sectionContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginBottom: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 20,
    textAlign: 'center',
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingRight: 8,
  },
  stepNumberContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: config.themeColor || '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  stepNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  stepText: {
    flex: 1,
    fontSize: 15,
    color: '#424242',
    lineHeight: 22,
  },
});

export default InfoModal;
