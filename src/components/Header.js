import React from 'react';
import {
  ImageBackground,
  StyleSheet,
  useWindowDimensions,
  View,
} from 'react-native';
const {isBankSms} = require('../Globals');

const Header = (props) => {
  const width = useWindowDimensions().width;
  const styles = StyleSheet.create({
    inlineImg: {
      flex: 1,
      marginHorizontal: width * 0.1,
    },
  });

  return (
    <View style={props.style}>
      {isBankSms && (
        <ImageBackground
          source={require('../resources/banksmslogo.png')}
          resizeMode={'contain'}
          style={styles.inlineImg}
        />
      )}
    </View>
  );
};

export default Header;
