import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  // Modal,
  Text,
  TouchableOpacity,
  Platform,
  Modal,
} from 'react-native';
import {AppContext} from '../contexts/AppContext';
import {strings, getAvailableLanguages, translate} from '../utils/Localization';
import {Picker} from '@react-native-picker/picker';
import config from 'react-native-config';

class LanguageModal extends Component {
  render() {
    const {modalVisible, setModalVisible} = this.props;
    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          setModalVisible(false);
        }}
        onBackdropPress={() => setModalVisible(false)}>
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <Text style={styles.title}>{strings('_languageTitle')}</Text>

            <Picker
              selectedValue={this.context.language}
              style={styles.picker}
              onValueChange={(itemValue, itemIndex) =>
                // setSelectedValue(itemValue)
                this.context.setLanguage(itemValue)
              }>
              {getAvailableLanguages().map(lang => (
                <Picker.Item
                  value={lang}
                  key={lang}
                  label={translate('_lang', lang)}
                />
              ))}
            </Picker>

            <View style={styles.buttons}>
              <TouchableOpacity
                style={styles.openButton}
                onPress={() => {
                  setModalVisible(!modalVisible);
                }}>
                <Text style={styles.textStyle}>{strings('_okayButton')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  }
}

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  picker: {height: 180, width: 150},
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  radioStyle: {
    alignSelf: 'flex-start',
    padding: 10,
  },
  openButton: {
    flex: 1,
    elevation: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttons: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textStyle: {
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 18,
    paddingTop: 10,
    paddingHorizontal: 20,
    color: config.themeColor,
    ...Platform.select({
      android: {fontFamily: 'Roboto'},
    }),
  },
  title: {
    fontWeight: 'bold',
    fontSize: 20,
    color: config.themeColor,
    alignSelf: 'center',
    ...Platform.select({
      android: {fontFamily: 'Roboto'},
    }),
  },
});

LanguageModal.contextType = AppContext;
export default LanguageModal;
