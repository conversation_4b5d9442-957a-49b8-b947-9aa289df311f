import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import {AppContext} from '../contexts/AppContext';
import {strings, setLanguage} from '../utils/Localization';
import Modal from 'react-native-modal';
import config from 'react-native-config';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const {height} = Dimensions.get('window');

const languages = [
  {code: 'en', name: 'English'},
  {code: 'ar', name: 'العربية'},
  {code: 'bg', name: 'Български'},
  {code: 'bs', name: '<PERSON><PERSON><PERSON>'},
  {code: 'cs', name: '<PERSON><PERSON><PERSON><PERSON>'},
  {code: 'de', name: '<PERSON><PERSON><PERSON>'},
  {code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'},
  {code: 'fa', name: 'فارسی'},
  {code: 'fr', name: 'Fran<PERSON>'},
  {code: 'hi', name: 'हिन्दी'},
  {code: 'it', name: 'Italiano'},
  {code: 'ja', name: '日本語'},
  {code: 'ko', name: '한국어'},
  {code: 'ms', name: 'Bahasa Melayu'},
  {code: 'pt', name: 'Português'},
  {code: 'ro', name: 'Română'},
  {code: 'ru', name: 'Русский'},
  {code: 'th', name: 'ไทย'},
  {code: 'tl', name: 'Tagalog'},
  {code: 'tr', name: 'Türkçe'},
];

class LanguageModal extends Component {
  handleLanguageSelect = (languageCode) => {
    this.context.setLanguage(languageCode);
    this.props.setModalVisible(false);
  };

  render() {
    const {modalVisible, setModalVisible} = this.props;

    return (
      <Modal
        isVisible={modalVisible}
        onBackdropPress={() => setModalVisible(false)}
        onSwipeComplete={() => setModalVisible(false)}
        swipeDirection={['down']}
        style={styles.modal}
        backdropOpacity={0.5}
        animationIn="slideInUp"
        animationOut="slideOutDown">
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.title}>{strings('_languageTitle')}</Text>
            <View style={styles.dragIndicator} />
          </View>
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}>
            <View style={styles.languageList}>
              {languages.map((lang) => (
                <TouchableOpacity
                  key={lang.code}
                  style={styles.languageButton}
                  onPress={() => this.handleLanguageSelect(lang.code)}>
                  <Icon
                    name="translate"
                    size={20}
                    color={config.themeColor}
                    style={styles.languageIcon}
                  />
                  <Text style={styles.languageText}>{lang.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
          <View style={styles.footer}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setModalVisible(false)}>
              <Text style={styles.closeButtonText}>
                {strings('_okayButton')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  }
}

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 8,
    maxHeight: height * 0.8,
  },
  modalHeader: {
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  dragIndicator: {
    width: 40,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#E0E0E0',
    position: 'absolute',
    top: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212121',
    marginTop: 8,
  },
  scrollView: {
    maxHeight: height * 0.6,
  },
  languageList: {
    padding: 16,
  },
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#F5F5F5',
  },
  languageIcon: {
    marginRight: 12,
  },
  languageText: {
    fontSize: 16,
    color: '#424242',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  closeButton: {
    backgroundColor: config.themeColor || '#2196F3',
    borderRadius: 20,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
});


LanguageModal.contextType = AppContext;
export default LanguageModal;
