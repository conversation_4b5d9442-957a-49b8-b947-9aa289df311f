/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {StyleSheet, View, TouchableOpacity, Image} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import InfoModal from './InfoModal';
import LanguageModal from './LanguageModal';
const {isBankSms} = require('../Globals');

const Info = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [languageModalVisible, setLanguageModalStateVisible] = useState(false);

  useEffect(() => {
    const value = AsyncStorage.getItem('once');

    if (value !== null) {
      value
        .then(ret => {
          if (ret === null) {
            AsyncStorage.setItem('once', 'yes');
            setLanguageModalStateVisible(true);
          }
        })
        .catch(err => {});
    }
  });

  const setLanguageModalVisible = languageModalVisible => {
    setLanguageModalStateVisible(languageModalVisible);
    if (languageModalVisible) {
      setModalVisible(!languageModalVisible);
    }
  };

  const styles = StyleSheet.create({
    infoView: {
      flex: 20,
      alignItems: 'flex-end',
    },
    banksmsInfoView: {
      flex: 80,
      flexDirection: 'column-reverse',
    },
    banksmsInfoImage: {
      flex: 1,
      flexDirection: 'row-reverse',
      alignContent: 'flex-start',
    },
    banksmsimage: {
      flex: 1,
      alignSelf: 'center',
      shadowOffset: {width: 0, height: 0},
      shadowRadius: 5,
    },
    flexOne: {flex: 1},
    flexTwo: {flex: 1.5},
  });
  const getInfo = () => {
    return (
      <View style={isBankSms ? styles.banksmsInfoView : styles.infoView}>
        <InfoModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          setLanguageModalVisible={setLanguageModalVisible}
        />
        <LanguageModal
          modalVisible={languageModalVisible}
          setModalVisible={setLanguageModalVisible}
        />
        <View style={[styles.banksmsInfoImage, isBankSms || styles.flexOne]}>
          <TouchableOpacity
            accessibilityLabel="Info"
            style={isBankSms ? styles.flexTwo : {flex: 18}}
            activeOpacity={1}
            onPress={() => {
              setModalVisible(true);
            }}>
            <Image
              style={styles.banksmsimage}
              resizeMode="contain"
              source={
                isBankSms
                  ? require('../resources/infowithborder.png')
                  : require('../resources/info.png')
              }
            />
          </TouchableOpacity>
          <View style={isBankSms ? styles.flexTwo : {flex: 19}} />
        </View>
        <View style={isBankSms ? styles.flexTwo : styles.flexOne} />
      </View>
    );
  };
  return getInfo();
};

export default Info;
