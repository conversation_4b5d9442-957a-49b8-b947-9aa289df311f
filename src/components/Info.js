import React, {useEffect, useState} from 'react';
import {StyleSheet, View, TouchableOpacity, Image} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import InfoModal from './InfoModal';
import LanguageModal from './LanguageModal';
const {isBankSms} = require('../Globals');

const Info = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [languageModalVisible, setLanguageModalStateVisible] = useState(false);

  useEffect(() => {
    const checkFirstVisit = async () => {
      try {
        const hasSeenInfo = await AsyncStorage.getItem('hasSeenInfo');

        if (hasSeenInfo === null) {
          await AsyncStorage.setItem('hasSeenInfo', 'yes');
          setModalVisible(true);
        }
      } catch (error) {
        console.error('Error checking first visit:', error);
      }
    };

    checkFirstVisit();
  }, []);

  const setLanguageModalVisible = languageModalVisible => {
    setLanguageModalStateVisible(languageModalVisible);
    if (languageModalVisible) {
      setModalVisible(!languageModalVisible);
    }
  };

  const styles = StyleSheet.create({
    infoView: {
      flex: 20,
      alignItems: 'flex-end',
      zIndex: 1,
    },
    banksmsInfoView: {
      flex: 80,
      flexDirection: 'column-reverse',
      zIndex: 1,
    },
    banksmsInfoImage: {
      flex: 1,
      flexDirection: 'row-reverse',
      alignContent: 'flex-start',
    },
    banksmsimage: {
      flex: 1,
      alignSelf: 'center',
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    flexOne: {flex: 1},
    flexTwo: {flex: 1.5},
  });
  
  const getInfo = () => {
    return (
      <View style={isBankSms ? styles.banksmsInfoView : styles.infoView}>
        <InfoModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          setLanguageModalVisible={setLanguageModalVisible}
        />
        <LanguageModal
          modalVisible={languageModalVisible}
          setModalVisible={setLanguageModalVisible}
        />
        <View style={[styles.banksmsInfoImage, isBankSms || styles.flexOne]}>
          <TouchableOpacity
            accessibilityLabel="Info"
            style={isBankSms ? styles.flexTwo : {flex: 18}}
            activeOpacity={0.7}
            onPress={() => setModalVisible(true)}>
            <Image
              style={styles.banksmsimage}
              resizeMode="contain"
              source={
                isBankSms
                  ? require('../resources/infowithborder.png')
                  : require('../resources/info.png')
              }
            />
          </TouchableOpacity>
          <View style={isBankSms ? styles.flexTwo : {flex: 19}} />
        </View>
        <View style={isBankSms ? styles.flexTwo : styles.flexOne} />
      </View>
    );
  };
  
  return getInfo();
};

export default Info;
