/* eslint-disable react-native/no-inline-styles */
import {ImageBackground, StyleSheet, SafeAreaView} from 'react-native';
import React, {Component} from 'react';
import Info from './Info';
import BannerAd from './BannerAd';
import Config from 'react-native-config';
const {isBankSms} = require('../Globals');

class InfoAndBackground extends Component {
  render() {
    // console.log(config.getConstants());
    var backgroundImage = isBankSms
      ? require('../resources/banksms_frame.png')
      : require('../resources/balancecheck_frame.jpg');
    return (
      <SafeAreaView style={{flex: 1}}>
        <BannerAd
          backgroundColor={Config.topAdBackgroundColor}
          textColor={Config.topAdTextColor}
          actionBtnColor={Config.topAdBtnColor}
          adId={Config.AdMobId_top}
        />
        <ImageBackground
          source={backgroundImage}
          resizeMode={'stretch'}
          style={styles.inlineImg}
          resizeMethod="auto">
          {isBankSms ? (
            <>
              <Info />
              {this.props.children}
            </>
          ) : (
            <>
              {this.props.children}
              <Info />
            </>
          )}
        </ImageBackground>
        <BannerAd
          backgroundColor={Config.bottomAdBackgroundColor}
          actionBtnColor={Config.bottomAdBtnColor}
          adId={Config.AdMobId_bottom}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  inlineImg: {
    flex: 1,
  },
});
export default InfoAndBackground;
