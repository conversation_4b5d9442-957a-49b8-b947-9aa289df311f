import { ErrorCode, PurchaseError } from 'react-native-iap';
import { strings } from './Localization';

export interface PaymentError {
  code: ErrorCode | string | undefined;
  message: string;
}

export class PaymentErrorHandler {
  private static errorMessageMap = new Map<string, string>([
    [ErrorCode.E_UNKNOWN, '_paymentDecline'],
    [ErrorCode.E_USER_CANCELLED, 'E_USER_CANCELLED'],
    [ErrorCode.E_SERVICE_ERROR, 'E_SERVICE_ERROR'],
    [ErrorCode.E_DEVELOPER_ERROR, 'E_DEVELOPER_ERROR'],
    [ErrorCode.E_ALREADY_OWNED, '_stillProcessing'],
    ['PROMISE_BUY_ITEM', 'E_SERVICE_ERROR'],
  ]);

  static handlePurchaseError(error: PurchaseError | PaymentError): string {
    if (!error.code) {
      return error.message;
    }
    const messageKey = this.errorMessageMap.get(error.code);
    if (messageKey) {
      return strings(messageKey);
    }
    return error.message;
  }

  static handleFinishTransactionError(error: PaymentError): string {
    if (!error.code) {
      return strings('_requestProcessing');
    }
    if (error.code === ErrorCode.E_DEVELOPER_ERROR) {
      return strings('E_DEVELOPER_ERROR');
    }
    return `finishTransaction, ${error.message}`;
  }
}
