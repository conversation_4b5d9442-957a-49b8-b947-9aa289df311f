import ReactNative from 'react-native';
import I18n from 'react-native-i18n';

// Import all locales
import cs from '../locales/cs.json';
import de from '../locales/de.json';
import en from '../locales/en.json';
import es from '../locales/es.json';
import fr from '../locales/fr.json';
import it from '../locales/it.json';
import ms from '../locales/ms.json';
import pt from '../locales/pt.json';
import ru from '../locales/ru.json';
import ar from '../locales/ar';
import fa from '../locales/fa.json';
import hi from '../locales/hi.json';
import ja from '../locales/ja.json';
import bg from '../locales/bg.json';
import bs from '../locales/bs.json';
import ko from '../locales/ko.json';
import tl from '../locales/tl.json';
import ro from '../locales/ro.json';
import tr from '../locales/tr.json';
import th from '../locales/th.json';

// Should the app fallback to English if user locale doesn't exists
I18n.fallbacks = true;

// Define the supported translations
const languages = {
  bs,
  cs,
  de,
  en,
  es,
  tl,
  fr,
  it,
  ms,
  pt,
  ru,
  ro,
  tr,
  hi,
  ja,
  bg,
  ko,
  th,
  ar,
  fa,
};
I18n.translations = languages;
export function getAvailableLanguages() {
  return Object.keys(languages);
}
export function translate(text, toLang) {
  return I18n.translate(text, {locale: toLang});
}
export function setAppLanguage(newLang) {
  I18n.locale = newLang;
}
// The method we'll use instead of a regular string
export function strings(name, params = {}) {
  return I18n.t(name, params);
}
