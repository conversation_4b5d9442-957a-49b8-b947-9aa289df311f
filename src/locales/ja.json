{"_makeTransaction": "今すぐチェック！", "_subscriptionConfirmTitle": "月額サブスクリプション", "_subscriptionConfirmMessage": "自動残高チェックのため、毎月%sが請求されます。このサブスクリプションは、キャンセルされるまで毎月自動的に更新されます。", "_confirmButton": "確認", "_cancelButton": "キャンセル", "_compatibilityTitle": "銀行互換性チェック", "_compatibilityMessage": "続行する前に、銀行が取引の詳細と現在の残高を含むSMS通知を送信することを確認してください。これらの通知は当社のサービスに不可欠です。銀行に確認してください。残高の詳細がない標準的な通知では不十分です。銀行がこの機能を提供していない場合、このアプリは残高を確認するのに役立ちません。", "_howItWorksTitle": "仕組み", "_step1": "Google Payでカードを使用して少額のテスト支払いが行われます（カードはすでにGoogleウォレットに追加されている必要があります）", "_step2": "これにより、銀行が現在の残高を含むSMSを送信します", "_step3": "SMSメッセージを確認して残高を確認します（アプリは残高を直接表示しません）", "_infoTitle": "情報", "_infoBody": "このアプリケーションは,オンラインバンキングを有効にしていない場合に備えて,銀行カードのクレジットを確認するのに役立ちます。\n\nアプリケーションは,すでにGoogleウォレットに追加したカードに,Googleの支払いで可能な最小額をサービスの料金として請求します。これは,場所によって異なります。これにより,銀行は残りのクレジットでSMSを送信します。\n\nこのサービスが期待どおりに機能するためには:\n-銀行が,カードでの購入ごとに残高を使ってSMSを送信するサービスをすでに有効にしていることを確認する必要があります。\n-カードがGoogleウォレットにすでに追加されていることを確認する必要があります。\n\nこのアプリケーションは銀行カード情報を保存せず,代わりにGoogleウォレットから直接受け取ります。ただし,実際のカード番号と詳細がアプリからアクセスされることはありません。", "_languageTitle": "言語", "_okayButton": "OK", "_rateApp": "レートアプリ", "_requestProcessing": "リクエストは処理中です", "_receiptTitle": "領収書", "_receiptBody": "間もなくSMSを受信します", "_paymentDecline": "支払いは拒否されました。カードをオンラインでの購入に使用できることと,カードに残高があることを確認してください", "E_USER_CANCELLED": "支払いはキャンセルされました。", "E_SERVICE_ERROR": "サービスにアクセスできません。 これはインターネット接続であるか,Playストアがダウンしている可能性があります。 ", "E_DEVELOPER_ERROR": "Googleは支払いに関連するいくつかの問題があることを示しています。", "E_ALREADY_OWNED": "あなたはすでにこのアイテムを所有しています。", "_lang": "日本語", "_successSubscribe": "SMSを毎月受信するはずです", "_subscribeButton": "毎月チェック！", "_stillProcessing": "すでに支払い済みです。", "_rateUs": "私たちのアプリが気に入ったら、私たちに5つ星を付けてください", "_terrible": "ひどい", "_bad": "悪い", "_disappointing": "がっかり", "_oK": "OK", "_good": "良い", "_contactUs": "お問い合わせ", "_subscribeMessage": "あなたは今月のSMSを受信するために購読しています"}