{
  "_makeTransaction": "Check Now!",
  "_infoTitle": "Info",
  "_infoBody": `This application helps you to check your bank card credit in case you don't have online banking enabled. 

  The application charges a card that you already added to your google wallet with the least amount possible allowed by Google payments as a fee for the service, which varies depending on your location. This triggers your bank to send you an SMS with the remaining credit. 
    
  For this service to work as expected:
  - You need to make sure that the bank has already activated the service of sending an SMS with your balance with every purchase made by the card.
  - You need to make sure that the card is already added to your google wallet.
    
  This application does not store your bank card info and instead receives them directly from your google wallet. However, the actual card number and details are never accessed by the app.`,
  "_languageTitle": "Language",
  "_okayButton": "Ok",
  "_rateApp": "Rate App",
  "_requestProcessing": "Your request is under processing",
  "_receiptTitle": "Receipt",
  "_receiptBody": "You will recieve SMS soon",
  "_paymentDecline": "Payment declined. Make sure your card can be used for online purchases and that there is balance on it",
  "E_USER_CANCELLED": "Payment is Cancelled",
  "E_SERVICE_ERROR": "The service is unreachable. This may be your internet connection, or the Play Store may be down",
  "E_DEVELOPER_ERROR": "Google is indicating that we have some issue connecting to payment",
  "E_ALREADY_OWNED": "You already own this item",
  "_lang": "English",
  "_successSubscribe": "You should now receive the SMS monthly",
  "_subscribeButton": "Check monthly!",
  "_stillProcessing": "You have already paid",
  "_rateUs": "If you like our app rate us five stars",
  "_terrible": "Terrible",
  "_bad": "Bad",
  "_disappointing": "disappointing",
  "_oK": "OK",
  "_good": "Good",
  "_contactUs": "Contact Us",
  "_subscribeMessage": "You are about to subscribe to get sms monthly for"
}
