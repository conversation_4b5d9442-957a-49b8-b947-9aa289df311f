{"_makeTransaction": "<PERSON><PERSON><PERSON> sekarang!", "_subscriptionConfirmTitle": "<PERSON><PERSON><PERSON>", "_subscriptionConfirmMessage": "Anda akan dikenakan bayaran %s setiap bulan untuk semakan baki automatik. Langganan ini akan diperbaharui secara automatik setiap bulan sehingga dibatalkan.", "_confirmButton": "<PERSON><PERSON><PERSON>", "_cancelButton": "<PERSON><PERSON>", "_compatibilityTitle": "Semak Keserasian Bank", "_compatibilityMessage": "Sebelum meneruskan, pastikan bank anda menghantar pemberitahuan SMS dengan butiran transaksi dan baki semasa anda. Pemberitahuan ini penting untuk perkhidmatan kami. Hubungi bank anda untuk mengesahkan. Pemberitahuan standard tanpa butiran baki tidak mencukupi. Jika bank anda tidak menyediakan ini, aplikasi ini tidak akan membantu dalam memeriksa baki.", "_howItWorksTitle": "Bagaimana <PERSON>", "_step1": "Pembayaran ujian kecil dibuat menggunakan kad anda dalam Google Pay (kad mesti sudah ditambahkan ke Google Wallet anda)", "_step2": "Ini mencetuskan bank anda untuk menghantar SMS dengan baki semasa anda", "_step3": "Periksa mesej SMS anda untuk melihat baki anda (aplikasi tidak memaparkan baki secara langsung)", "_infoTitle": "Maklumat", "_infoBody": "Aplikasi ini membantu anda memeriksa kredit kad bank anda sekiranya anda tidak mengaktifkan perbankan dalam talian.\n\nAplikasi mengenakan kad yang telah anda tambahkan ke dompet google anda dengan jumlah paling sedikit yang dibenarkan oleh pembayaran Google sebagai bayaran untuk perkhidmatan, yang berbeza-beza bergantung pada lokasi anda. Ini mendorong bank anda untuk menghantar sms kepada anda dengan baki kredit.\n\nAgar perkhidmatan ini dapat berfungsi seperti yang diharapkan:\n- Anda perlu memastikan bahawa bank telah mengaktifkan perkhidmatan menghantar sms dengan baki anda dengan setiap pembelian yang dilakukan oleh kad.\n- Anda perlu memastikan bahawa kad sudah ditambahkan ke dompet google anda.\n\nAplikasi ini tidak menyimpan maklumat kad bank anda dan sebaliknya menerimanya terus dari dompet google anda. <PERSON><PERSON><PERSON> bagai<PERSON>pun, nombor dan butiran kad sebenar tidak pernah dapat diakses oleh aplikasi.", "_languageTitle": "Bahasa", "_okayButton": "Ok", "_rateApp": "<PERSON><PERSON>", "_requestProcessing": "<PERSON><PERSON><PERSON><PERSON> anda dalam proses", "_receiptTitle": "Resit", "_receiptBody": "<PERSON><PERSON> akan menerima SMS tidak lama lagi", "_pembayaranTolak": "<PERSON><PERSON><PERSON><PERSON> ditolak. <PERSON><PERSON><PERSON> kad anda dapat digunakan untuk pembelian dalam talian dan ada baki padanya ", "E_USER_CANCELLED": "Pembayaran Dibatalkan", "E_SERVICE_ERROR": "Perkhidmatan ini tidak dapat dicapai. Ini mungkin sambungan internet anda, atau Play Store mungkin tidak berfungsi. ", "E_DEVELOPER_ERROR": "Google menunjukkan bahawa kami mempunyai beberapa masalah berkaitan dengan pembayaran", "E_ALREADY_OWNED": "Anda sudah memiliki item ini", "_lang": "<PERSON><PERSON><PERSON>", "_successSubscribe": "<PERSON><PERSON> se<PERSON>ng harus menerima SMS setiap bulan", "_subscribeButton": "<PERSON><PERSON><PERSON> set<PERSON>p bulan!", "_stillProcessing": "<PERSON>a sudah membayar", "_rateUs": "<PERSON><PERSON><PERSON><PERSON> anda suka aplikasi kami beri kami lima bintang", "_terrible": "Dahsyat", "_bad": "Buruk", "_disappointing": "mengecewakan", "_oK": "OK", "_good": "Baik", "_contactUs": "<PERSON><PERSON><PERSON><PERSON>", "_subscribeMessage": "<PERSON><PERSON> akan menerima SMS bulanan untuk"}