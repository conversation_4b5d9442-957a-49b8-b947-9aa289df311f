import React, {createContext, Component} from 'react';

import {NativeModules, Platform} from 'react-native';
import {setAppLanguage} from '../utils/Localization';

export const AppContext = createContext();
import AsyncStorage from '@react-native-async-storage/async-storage';

class AppContextProvider extends Component {
  constructor(props) {
    super(props);
    const value = AsyncStorage.getItem('language');

    if (value !== null) {
      value
        .then((language) => {
          if (language !== null) {
            this.setState({
              language: language,
            });
          }
        })
        .catch((err) => {});
    }
  }
  state = {
    language: Platform.OS=='ios'?NativeModules.SettingsManager.settings.AppleLocale ||
    NativeModules.SettingsManager.settings.AppleLanguages[0].split('_')[0] // "fr_FR"
: NativeModules.I18nManager.localeIdentifier.split('_')[0],
  };
  setLanguage = (language) => {
    this.setState({language: language});
    AsyncStorage.setItem('language', language);
  };

  render() {
    setAppLanguage(this.state.language);
    return (
      <AppContext.Provider
        value={{
          ...this.state,
          setLanguage: this.setLanguage,
        }}>
        {this.props.children}
      </AppContext.Provider>
    );
  }
}

export default AppContextProvider;
