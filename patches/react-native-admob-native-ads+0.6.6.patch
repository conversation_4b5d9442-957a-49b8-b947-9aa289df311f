diff --git a/node_modules/react-native-admob-native-ads/android/build/.transforms/6ff2a46ffcf34375fa91fc545c37c23d/results.bin b/node_modules/react-native-admob-native-ads/android/build/.transforms/6ff2a46ffcf34375fa91fc545c37c23d/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/.transforms/6ff2a46ffcf34375fa91fc545c37c23d/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/react-native-admob-native-ads/android/build/.transforms/6ff2a46ffcf34375fa91fc545c37c23d/transformed/classes/classes_dex/classes.dex b/node_modules/react-native-admob-native-ads/android/build/.transforms/6ff2a46ffcf34375fa91fc545c37c23d/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..8c6b23b
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/.transforms/6ff2a46ffcf34375fa91fc545c37c23d/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/generated/source/buildConfig/debug/com/ammarahmed/rnadmob/nativeads/BuildConfig.java b/node_modules/react-native-admob-native-ads/android/build/generated/source/buildConfig/debug/com/ammarahmed/rnadmob/nativeads/BuildConfig.java
new file mode 100644
index 0000000..1d937e0
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/generated/source/buildConfig/debug/com/ammarahmed/rnadmob/nativeads/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.ammarahmed.rnadmob.nativeads;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.ammarahmed.rnadmob.nativeads";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/react-native-admob-native-ads/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..5b886ad
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.ammarahmed.rnadmob.nativeads" >
+
+    <uses-sdk android:minSdkVersion="23" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/react-native-admob-native-ads/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..bed05f7
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.ammarahmed.rnadmob.nativeads",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-admob-native-ads/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/react-native-admob-native-ads/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/react-native-admob-native-ads/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..ba0d22e
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/react-native-admob-native-ads/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..a36944d
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/react-native-admob-native-ads/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..0a72861
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/compile_symbol_list/debug/R.txt
@@ -0,0 +1,2 @@
+int id native_ad_view 0x0
+int layout rn_ad_unified_native_ad 0x0
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/compiled_local_resources/debug/out/layout_rn_ad_unified_native_ad.xml.flat b/node_modules/react-native-admob-native-ads/android/build/intermediates/compiled_local_resources/debug/out/layout_rn_ad_unified_native_ad.xml.flat
new file mode 100644
index 0000000..609b177
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/compiled_local_resources/debug/out/layout_rn_ad_unified_native_ad.xml.flat differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..148b1c0
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1,2 @@
+#Wed Jul 17 16:10:37 EEST 2024
+com.ammarahmed.rnadmob.nativeads.react-native-admob-native-ads-main-6\:/layout/rn_ad_unified_native_ad.xml=/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/build/intermediates/packaged_res/debug/layout/rn_ad_unified_native_ad.xml
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..f3b4b0f
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/res"><file name="rn_ad_unified_native_ad" path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/res/layout/rn_ad_unified_native_ad.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..e8cb865
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..93afc60
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..99cfc76
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/build/intermediates/shader_assets/debug/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/BuildConfig.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/BuildConfig.class
new file mode 100644
index 0000000..2b39709
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/BuildConfig.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/CacheManager.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/CacheManager.class
new file mode 100644
index 0000000..a9ddd97
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/CacheManager.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/EventEmitter.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/EventEmitter.class
new file mode 100644
index 0000000..a3ec121
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/EventEmitter.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobNativePackage.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobNativePackage.class
new file mode 100644
index 0000000..71b6549
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobNativePackage.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdComparator.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdComparator.class
new file mode 100644
index 0000000..b581602
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdComparator.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdContainer.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdContainer.class
new file mode 100644
index 0000000..3531592
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdContainer.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper$1$1.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper$1$1.class
new file mode 100644
index 0000000..11d3872
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper$1$1.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper$1.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper$1.class
new file mode 100644
index 0000000..ae81b3a
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper$1.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper.class
new file mode 100644
index 0000000..e4abf9d
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobAdChoicesManager.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobAdChoicesManager.class
new file mode 100644
index 0000000..90d7693
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobAdChoicesManager.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobButton.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobButton.class
new file mode 100644
index 0000000..f850707
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobButton.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobButtonManager.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobButtonManager.class
new file mode 100644
index 0000000..4155843
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobButtonManager.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobComponentsWrapper$1.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobComponentsWrapper$1.class
new file mode 100644
index 0000000..70ec59f
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobComponentsWrapper$1.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobComponentsWrapper.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobComponentsWrapper.class
new file mode 100644
index 0000000..ca38287
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobComponentsWrapper.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobComponentsWrapperManager.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobComponentsWrapperManager.class
new file mode 100644
index 0000000..8f5b31d
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobComponentsWrapperManager.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaView$1.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaView$1.class
new file mode 100644
index 0000000..b609fc5
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaView$1.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaView$2.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaView$2.class
new file mode 100644
index 0000000..a49adbc
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaView$2.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaView.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaView.class
new file mode 100644
index 0000000..8fd82c5
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaView.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaViewManager.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaViewManager.class
new file mode 100644
index 0000000..f5c0109
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobMediaViewManager.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeAdsManager.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeAdsManager.class
new file mode 100644
index 0000000..f88c9bf
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeAdsManager.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView$1.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView$1.class
new file mode 100644
index 0000000..f2241fd
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView$1.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView$2.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView$2.class
new file mode 100644
index 0000000..bbc3e5d
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView$2.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView.class
new file mode 100644
index 0000000..3b44f1b
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeViewManager.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeViewManager.class
new file mode 100644
index 0000000..e627692
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeViewManager.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/UnifiedNativeAdLoadedListener.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/UnifiedNativeAdLoadedListener.class
new file mode 100644
index 0000000..1cce85a
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/UnifiedNativeAdLoadedListener.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/Utils.class b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/Utils.class
new file mode 100644
index 0000000..e3df979
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/javac/debug/classes/com/ammarahmed/rnadmob/nativeads/Utils.class differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/react-native-admob-native-ads/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..b243a8c
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,4 @@
+R_DEF: Internal format may change without notice
+local
+id native_ad_view
+layout rn_ad_unified_native_ad
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/react-native-admob-native-ads/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..2b2bb4f
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.ammarahmed.rnadmob.nativeads" >
+4
+5    <uses-sdk android:minSdkVersion="23" />
+6
+7    <uses-permission android:name="android.permission.INTERNET" />
+7-->/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml:4:5-67
+7-->/Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml:4:22-64
+8
+9</manifest>
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/react-native-admob-native-ads/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..5b886ad
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.ammarahmed.rnadmob.nativeads" >
+
+    <uses-sdk android:minSdkVersion="23" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-admob-native-ads/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/packaged_res/debug/layout/rn_ad_unified_native_ad.xml b/node_modules/react-native-admob-native-ads/android/build/intermediates/packaged_res/debug/layout/rn_ad_unified_native_ad.xml
new file mode 100755
index 0000000..1d84177
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/packaged_res/debug/layout/rn_ad_unified_native_ad.xml
@@ -0,0 +1,10 @@
+<?xml version="1.0" encoding="utf-8"?>
+<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:app="http://schemas.android.com/apk/res-auto"
+    xmlns:tools="http://schemas.android.com/tools"
+    android:background="@android:color/transparent"
+    android:id="@+id/native_ad_view"
+    android:layout_width="match_parent"
+    android:layout_height="wrap_content">
+
+</com.google.android.gms.ads.nativead.NativeAdView>
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/react-native-admob-native-ads/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..53bf9e8
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-admob-native-ads/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/react-native-admob-native-ads/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..452df1d
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1,3 @@
+com.ammarahmed.rnadmob.nativeads
+id native_ad_view
+layout rn_ad_unified_native_ad
diff --git a/node_modules/react-native-admob-native-ads/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-admob-native-ads/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..2b8aa3c
--- /dev/null
+++ b/node_modules/react-native-admob-native-ads/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,21 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml:1:1-5:12
+INJECTED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml:1:1-5:12
+	package
+		ADDED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml:2:11-53
+		INJECTED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml:1:11-69
+uses-permission#android.permission.INTERNET
+ADDED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml:4:5-67
+	android:name
+		ADDED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml:4:22-64
+uses-sdk
+INJECTED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Desktop/Workspaces/personal/balance-check/node_modules/react-native-admob-native-ads/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-admob-native-ads/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/react-native-admob-native-ads/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..f4ed6a4
Binary files /dev/null and b/node_modules/react-native-admob-native-ads/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/react-native-admob-native-ads/android/src/main/java/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper.java b/node_modules/react-native-admob-native-ads/android/src/main/java/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper.java
index cc1d3e8..9382a83 100644
--- a/node_modules/react-native-admob-native-ads/android/src/main/java/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper.java
+++ b/node_modules/react-native-admob-native-ads/android/src/main/java/com/ammarahmed/rnadmob/nativeads/RNAdMobUnifiedAdQueueWrapper.java
@@ -205,7 +205,7 @@ public class RNAdMobUnifiedAdQueueWrapper {
         }
 
         if (config.hasKey("requestNonPersonalizedAdsOnly")) {
-            Utils.setRequestNonPersonalizedAdsOnly(config.getBoolean("requestNonPersonalizedAdsOnly"), adRequest);
+            // Utils.setRequestNonPersonalizedAdsOnly(config.getBoolean("requestNonPersonalizedAdsOnly"), adRequest);
         }
 
         if (config.hasKey("mediaAspectRatio")) {
diff --git a/node_modules/react-native-admob-native-ads/android/src/main/java/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView.java b/node_modules/react-native-admob-native-ads/android/src/main/java/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView.java
index 3a6443b..276fb17 100644
--- a/node_modules/react-native-admob-native-ads/android/src/main/java/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView.java
+++ b/node_modules/react-native-admob-native-ads/android/src/main/java/com/ammarahmed/rnadmob/nativeads/RNAdmobNativeView.java
@@ -375,7 +375,7 @@ public class RNAdmobNativeView extends LinearLayout {
     }
 
     public void setRequestNonPersonalizedAdsOnly(boolean npa) {
-        Utils.setRequestNonPersonalizedAdsOnly(npa, adRequest);
+        // Utils.setRequestNonPersonalizedAdsOnly(npa, adRequest);
 
     }
 
