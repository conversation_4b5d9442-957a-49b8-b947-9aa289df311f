diff --git a/src/specs/modules/NativeGoogleMobileAdsNativeModule.ts b/src/specs/modules/NativeGoogleMobileAdsNativeModule.ts
index 2b5c8c0..a6d5b5a 100644
--- a/src/specs/modules/NativeGoogleMobileAdsNativeModule.ts
+++ b/src/specs/modules/NativeGoogleMobileAdsNativeModule.ts
@@ -58,7 +58,6 @@ export type NativeAdEventPayload = {
 export interface Spec extends TurboModule {
   load(adUnitId: string, requestOptions: UnsafeObject): Promise<NativeAdProps>;
   destroy(responseId: string): void;
-  readonly onAdEvent: EventEmitter<NativeAdEventPayload>;
 }
 
 export default TurboModuleRegistry.getEnforcing<Spec>('RNGoogleMobileAdsNativeModule');
diff --git a/lib/typescript/specs/modules/NativeGoogleMobileAdsNativeModule.d.ts b/lib/typescript/specs/modules/NativeGoogleMobileAdsNativeModule.d.ts
index 2b5c8c0..a6d5b5a 100644
--- a/lib/typescript/specs/modules/NativeGoogleMobileAdsNativeModule.d.ts
+++ b/lib/typescript/specs/modules/NativeGoogleMobileAdsNativeModule.d.ts
@@ -30,7 +30,6 @@ export type NativeAdEventPayload = {
 export interface Spec extends TurboModule {
     load(adUnitId: string, requestOptions: UnsafeObject): Promise<NativeAdProps>;
     destroy(responseId: string): void;
-    readonly onAdEvent: EventEmitter<NativeAdEventPayload>;
 }
 export default _default;
 //# sourceMappingURL=NativeGoogleMobileAdsNativeModule.d.ts.map